import { BackgroundType } from '../components/common/AnimatedBackground/AnimatedBackground';
import { AnimationType } from '../components/common/PageAnimation/PageAnimation';

export interface PageAnimationConfig {
  background?: {
    type: BackgroundType;
    intensity?: 'low' | 'medium' | 'high';
    color?: 'primary' | 'secondary' | 'accent' | 'neutral';
  };
  animation?: {
    type: AnimationType;
    duration?: number;
    delay?: number;
    stagger?: boolean;
    staggerDelay?: number;
  };
}

export const pageAnimationConfigs: Record<string, PageAnimationConfig> = {
  home: {
    background: {
      type: 'gradient-waves',
      intensity: 'medium',
      color: 'primary'
    },
    animation: {
      type: 'fade-in',
      duration: 0.8,
      stagger: true,
      staggerDelay: 0.2
    }
  },
  
  about: {
    background: {
      type: 'floating-shapes',
      intensity: 'low',
      color: 'secondary'
    },
    animation: {
      type: 'slide-up',
      duration: 0.6,
      delay: 0.1
    }
  },
  
  projects: {
    background: {
      type: 'geometric',
      intensity: 'medium',
      color: 'accent'
    },
    animation: {
      type: 'scale-up',
      duration: 0.7,
      stagger: true,
      staggerDelay: 0.1
    }
  },
  
  contact: {
    background: {
      type: 'aurora',
      intensity: 'high',
      color: 'primary'
    },
    animation: {
      type: 'slide-right',
      duration: 0.6
    }
  },
  
  'blog-editor': {
    background: {
      type: 'code-rain',
      intensity: 'low',
      color: 'primary'
    },
    animation: {
      type: 'fade-in',
      duration: 0.8,
      stagger: true,
      staggerDelay: 0.15
    }
  },
  
  'project-detail': {
    background: {
      type: 'minimal-dots',
      intensity: 'medium',
      color: 'neutral'
    },
    animation: {
      type: 'slide-up',
      duration: 0.6
    }
  }
};

// Helper function to get page config by route
export const getPageConfig = (pathname: string): PageAnimationConfig => {
  // Remove leading slash and get the first segment
  const route = pathname.replace('/', '').split('/')[0] || 'home';
  
  // Handle special cases
  if (pathname.includes('/projects/') && pathname !== '/projects') {
    return pageAnimationConfigs['project-detail'];
  }
  
  return pageAnimationConfigs[route] || pageAnimationConfigs.home;
};

// Alternative configurations for different themes or seasons
export const alternativeConfigs = {
  dark: {
    'blog-editor': {
      background: {
        type: 'particles',
        intensity: 'medium',
        color: 'primary'
      },
      animation: {
        type: 'fade-in',
        duration: 0.8
      }
    }
  },
  
  minimal: {
    'blog-editor': {
      background: {
        type: 'grid-pattern',
        intensity: 'low',
        color: 'neutral'
      },
      animation: {
        type: 'slide-up',
        duration: 0.5
      }
    }
  }
};