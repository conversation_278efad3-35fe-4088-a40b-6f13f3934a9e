import { useState, useEffect } from 'react';
import { breakpoints } from '../theme';

type Breakpoint = keyof typeof breakpoints;

/**
 * Hook to get current breakpoint and responsive utilities
 */
export const useBreakpoint = () => {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<Breakpoint>('sm');
  const [windowWidth, setWindowWidth] = useState<number>(0);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setWindowWidth(width);

      if (width >= parseInt(breakpoints['2xl'])) {
        setCurrentBreakpoint('2xl');
      } else if (width >= parseInt(breakpoints.xl)) {
        setCurrentBreakpoint('xl');
      } else if (width >= parseInt(breakpoints.lg)) {
        setCurrentBreakpoint('lg');
      } else if (width >= parseInt(breakpoints.md)) {
        setCurrentBreakpoint('md');
      } else {
        setCurrentBreakpoint('sm');
      }
    };

    // Set initial values
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const isAbove = (breakpoint: Breakpoint): boolean => {
    const breakpointValues = { sm: 640, md: 768, lg: 1024, xl: 1280, '2xl': 1536 };
    return windowWidth >= breakpointValues[breakpoint];
  };

  const isBelow = (breakpoint: Breakpoint): boolean => {
    const breakpointValues = { sm: 640, md: 768, lg: 1024, xl: 1280, '2xl': 1536 };
    return windowWidth < breakpointValues[breakpoint];
  };

  return {
    currentBreakpoint,
    windowWidth,
    isAbove,
    isBelow,
    isMobile: isBelow('md'),
    isTablet: isAbove('md') && isBelow('lg'),
    isDesktop: isAbove('lg'),
  };
};

export default useBreakpoint;