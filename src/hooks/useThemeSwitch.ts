import { useEffect, useCallback } from 'react';
import { ThemeMode } from '../types/common';
import { useLocalStorage } from './useLocalStorage';

/**
 * Enhanced theme switch hook return type
 */
export interface ThemeState {
  theme: ThemeMode;
  toggleTheme: () => void;
  setTheme: (newTheme: ThemeMode) => void;
  isDarkMode: boolean;
}

/**
 * Enhanced theme switch hook with better persistence and system preference detection
 * @param {ThemeMode} defaultTheme - Default theme ('light' or 'dark')
 * @returns {ThemeState} - Theme state and methods
 */
const useThemeSwitch = (defaultTheme: ThemeMode = 'light'): ThemeState => {
  // Detect system preference
  const getSystemPreference = (): ThemeMode => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return defaultTheme;
  };

  // Use localStorage hook with system preference fallback
  const [theme, setTheme] = useLocalStorage<ThemeMode>('theme', getSystemPreference());
  
  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Remove all theme classes
    root.classList.remove('theme-light', 'theme-dark');
    
    // Add current theme class
    root.classList.add(`theme-${theme}`);
    
    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', theme === 'dark' ? '#111827' : '#ffffff');
    }
  }, [theme]);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      // Only update if user hasn't manually set a preference
      const savedTheme = localStorage.getItem('theme');
      if (!savedTheme) {
        setTheme(e.matches ? 'dark' : 'light');
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [setTheme]);
  
  // Toggle theme function
  const toggleTheme = useCallback((): void => {
    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');
  }, [setTheme]);
  
  // Set specific theme function
  const setSpecificTheme = useCallback((newTheme: ThemeMode): void => {
    if (newTheme === 'light' || newTheme === 'dark') {
      setTheme(newTheme);
    }
  }, [setTheme]);
  
  return {
    theme,
    toggleTheme,
    setTheme: setSpecificTheme,
    isDarkMode: theme === 'dark'
  };
};

export default useThemeSwitch;
