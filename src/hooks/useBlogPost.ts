import { useState, useEffect, useCallback, useRef } from 'react';
import { BlogPost, BlogOperationResult } from '../types/common';
import { useBlogStorage } from './useBlogStorage';

export interface UseBlogPostOptions {
  blogId?: string;
  autoSave?: boolean;
  autoSaveInterval?: number;
  enableHistory?: boolean;
}

export interface UseBlogPostReturn {
  // Data
  blog: BlogPost | null;
  originalBlog: BlogPost | null;
  hasUnsavedChanges: boolean;
  
  // State
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
  lastSaved: Date | null;
  
  // Actions
  loadBlog: (id: string) => Promise<void>;
  saveBlog: () => Promise<BlogOperationResult<BlogPost>>;
  createNewBlog: (initialData?: Partial<BlogPost>) => void;
  updateTitle: (title: string) => void;
  updateContent: (content: string) => void;
  updateExcerpt: (excerpt: string) => void;
  updateAuthor: (author: string) => void;
  updateTags: (tags: string[]) => void;
  addTag: (tag: string) => void;
  removeTag: (tag: string) => void;
  updateCategory: (category: string) => void;
  updateThumbnail: (thumbnail: string) => void;
  updateStatus: (status: 'draft' | 'published' | 'archived') => void;
  publishBlog: () => void;
  unpublishBlog: () => void;
  archiveBlog: () => void;
  
  // History
  addModification: (description: string, author?: string) => void;
  
  // Utilities
  resetChanges: () => void;
  clearError: () => void;
  getWordCount: () => number;
  getCharacterCount: () => number;
  getReadingTime: () => number;
}

export const useBlogPost = (options: UseBlogPostOptions = {}): UseBlogPostReturn => {
  const {
    blogId,
    autoSave = false,
    autoSaveInterval = 30000,
    enableHistory = true
  } = options;

  const [blog, setBlog] = useState<BlogPost | null>(null);
  const [originalBlog, setOriginalBlog] = useState<BlogPost | null>(null);
  const blogRef = useRef<BlogPost | null>(null);

  const {
    isSaving,
    isLoading,
    lastSaved,
    error,
    saveBlog: saveToStorage,
    loadBlog: loadFromStorage,
    createNewBlog: createNew,
    updateBlogContent,
    updateBlogMetadata,
    addModificationEntry,
    enableAutoSave,
    disableAutoSave,
    clearError
  } = useBlogStorage({ autoSave, autoSaveInterval });

  // Update ref when blog changes
  useEffect(() => {
    blogRef.current = blog;
  }, [blog]);

  // Check for unsaved changes
  const hasUnsavedChanges = blog && originalBlog ? 
    JSON.stringify(blog) !== JSON.stringify(originalBlog) : false;

  const loadBlog = useCallback(async (id: string) => {
    const result = await loadFromStorage(id);
    if (result.success && result.data) {
      setBlog(result.data);
      setOriginalBlog(JSON.parse(JSON.stringify(result.data))); // Deep clone
    }
  }, [loadFromStorage]);

  const saveBlog = useCallback(async (): Promise<BlogOperationResult<BlogPost>> => {
    if (!blog) {
      return {
        success: false,
        error: {
          code: 'NO_BLOG',
          message: 'No blog to save'
        }
      };
    }

    const result = await saveToStorage(blog);
    if (result.success && result.data) {
      setOriginalBlog(JSON.parse(JSON.stringify(result.data))); // Deep clone
    }
    return result;
  }, [blog, saveToStorage]);

  const createNewBlog = useCallback((initialData?: Partial<BlogPost>) => {
    const newBlog = createNew(initialData);
    setBlog(newBlog);
    setOriginalBlog(JSON.parse(JSON.stringify(newBlog))); // Deep clone
  }, [createNew]);

  const updateTitle = useCallback((title: string) => {
    if (!blog) return;
    const updatedBlog = updateBlogMetadata(blog, { title });
    setBlog(updatedBlog);
  }, [blog, updateBlogMetadata]);

  const updateContent = useCallback((content: string) => {
    if (!blog) return;
    const updatedBlog = updateBlogContent(blog, content);
    setBlog(updatedBlog);
  }, [blog, updateBlogContent]);

  const updateExcerpt = useCallback((excerpt: string) => {
    if (!blog) return;
    const updatedBlog = updateBlogMetadata(blog, { excerpt });
    setBlog(updatedBlog);
  }, [blog, updateBlogMetadata]);

  const updateAuthor = useCallback((author: string) => {
    if (!blog) return;
    const updatedBlog = updateBlogMetadata(blog, { author });
    setBlog(updatedBlog);
  }, [blog, updateBlogMetadata]);

  const updateTags = useCallback((tags: string[]) => {
    if (!blog) return;
    const updatedBlog = updateBlogMetadata(blog, { tags });
    setBlog(updatedBlog);
  }, [blog, updateBlogMetadata]);

  const addTag = useCallback((tag: string) => {
    if (!blog || blog.tags.includes(tag)) return;
    const updatedTags = [...blog.tags, tag];
    updateTags(updatedTags);
  }, [blog, updateTags]);

  const removeTag = useCallback((tag: string) => {
    if (!blog) return;
    const updatedTags = blog.tags.filter(t => t !== tag);
    updateTags(updatedTags);
  }, [blog, updateTags]);

  const updateCategory = useCallback((category: string) => {
    if (!blog) return;
    const updatedBlog = updateBlogMetadata(blog, { category });
    setBlog(updatedBlog);
  }, [blog, updateBlogMetadata]);

  const updateThumbnail = useCallback((thumbnail: string) => {
    if (!blog) return;
    const updatedBlog = updateBlogMetadata(blog, { thumbnail });
    setBlog(updatedBlog);
  }, [blog, updateBlogMetadata]);

  const updateStatus = useCallback((status: 'draft' | 'published' | 'archived') => {
    if (!blog) return;
    const updatedBlog = updateBlogMetadata(blog, { status });
    setBlog(updatedBlog);
  }, [blog, updateBlogMetadata]);

  const addModification = useCallback((description: string, author?: string) => {
    if (!blog) return;
    const updatedBlog = addModificationEntry(blog, description, author || blog.author);
    setBlog(updatedBlog);
  }, [blog, addModificationEntry]);

  const publishBlog = useCallback(() => {
    if (!blog) return;
    const now = new Date();
    const updatedBlog = updateBlogMetadata(blog, { 
      status: 'published',
      publishedAt: now
    });
    setBlog(updatedBlog);
    
    if (enableHistory) {
      addModification('Blog published', blog.author);
    }
  }, [blog, updateBlogMetadata, enableHistory, addModification]);

  const unpublishBlog = useCallback(() => {
    if (!blog) return;
    const updatedBlog = updateBlogMetadata(blog, { 
      status: 'draft',
      publishedAt: undefined
    });
    setBlog(updatedBlog);
    
    if (enableHistory) {
      addModification('Blog unpublished', blog.author);
    }
  }, [blog, updateBlogMetadata, enableHistory, addModification]);

  const archiveBlog = useCallback(() => {
    if (!blog) return;
    const updatedBlog = updateBlogMetadata(blog, { status: 'archived' });
    setBlog(updatedBlog);
    
    if (enableHistory) {
      addModification('Blog archived', blog.author);
    }
  }, [blog, updateBlogMetadata, enableHistory, addModification]);

  const resetChanges = useCallback(() => {
    if (originalBlog) {
      setBlog(JSON.parse(JSON.stringify(originalBlog))); // Deep clone
    }
  }, [originalBlog]);

  const getWordCount = useCallback(() => {
    if (!blog) return 0;
    return blog.content.trim().split(/\s+/).filter(word => word.length > 0).length;
  }, [blog]);

  const getCharacterCount = useCallback(() => {
    if (!blog) return 0;
    return blog.content.length;
  }, [blog]);

  const getReadingTime = useCallback(() => {
    if (!blog) return 0;
    return blog.readingTime;
  }, [blog]);

  // Auto-save setup
  useEffect(() => {
    if (autoSave && blog) {
      enableAutoSave(blog, autoSaveInterval);
    } else {
      disableAutoSave();
    }

    return () => disableAutoSave();
  }, [autoSave, blog, autoSaveInterval, enableAutoSave, disableAutoSave]);

  // Load initial blog if blogId is provided
  useEffect(() => {
    if (blogId) {
      loadBlog(blogId);
    }
  }, [blogId, loadBlog]);

  return {
    // Data
    blog,
    originalBlog,
    hasUnsavedChanges,
    
    // State
    isLoading,
    isSaving,
    error,
    lastSaved,
    
    // Actions
    loadBlog,
    saveBlog,
    createNewBlog,
    updateTitle,
    updateContent,
    updateExcerpt,
    updateAuthor,
    updateTags,
    addTag,
    removeTag,
    updateCategory,
    updateThumbnail,
    updateStatus,
    publishBlog,
    unpublishBlog,
    archiveBlog,
    
    // History
    addModification,
    
    // Utilities
    resetChanges,
    clearError,
    getWordCount,
    getCharacterCount,
    getReadingTime
  };
};
