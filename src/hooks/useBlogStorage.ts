import { useState, useCallback, useRef } from 'react';
import { BlogPost, BlogOperationResult, ModificationEntry } from '../types/common';
import { 
  BlogLocalStorage, 
  generateBlogId, 
  generateSlug, 
  calculateReadingTime, 
  extractExcerpt,
  BLOG_STORAGE_CONFIG 
} from '../utils/blogStorage';

export interface UseBlogStorageOptions {
  autoSave?: boolean;
  autoSaveInterval?: number;
}

export interface UseBlogStorageReturn {
  // State
  isSaving: boolean;
  isLoading: boolean;
  lastSaved: Date | null;
  error: string | null;
  
  // Actions
  saveBlog: (blog: Partial<BlogPost>) => Promise<BlogOperationResult<BlogPost>>;
  loadBlog: (id: string) => Promise<BlogOperationResult<BlogPost>>;
  deleteBlog: (id: string) => Promise<BlogOperationResult<void>>;
  createNewBlog: (initialData?: Partial<BlogPost>) => BlogPost;
  updateBlogContent: (blog: BlogPost, content: string) => BlogPost;
  updateBlogMetadata: (blog: BlogPost, metadata: Partial<BlogPost>) => BlogPost;
  addModificationEntry: (blog: BlogPost, description: string, author: string) => BlogPost;
  
  // Auto-save
  enableAutoSave: (blog: BlogPost, interval?: number) => void;
  disableAutoSave: () => void;
  
  // Utilities
  clearError: () => void;
}

export const useBlogStorage = (options: UseBlogStorageOptions = {}): UseBlogStorageReturn => {
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);
  const autoSaveBlogRef = useRef<BlogPost | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const saveBlog = useCallback(async (blogData: Partial<BlogPost>): Promise<BlogOperationResult<BlogPost>> => {
    setIsSaving(true);
    setError(null);

    try {
      // Ensure required fields are present
      const now = new Date();
      const blog: BlogPost = {
        id: blogData.id || generateBlogId(),
        title: blogData.title || '',
        content: blogData.content || '',
        excerpt: blogData.excerpt || extractExcerpt(blogData.content || ''),
        author: blogData.author || 'Anonymous',
        createdAt: blogData.createdAt || now,
        updatedAt: now,
        publishedAt: blogData.publishedAt,
        tags: blogData.tags || [],
        category: blogData.category,
        thumbnail: blogData.thumbnail,
        readingTime: calculateReadingTime(blogData.content || ''),
        status: blogData.status || 'draft',
        slug: blogData.slug || generateSlug(blogData.title || ''),
        modificationHistory: blogData.modificationHistory || []
      };

      const result = BlogLocalStorage.saveBlog(blog);
      
      if (result.success) {
        setLastSaved(now);
      } else {
        setError(result.error?.message || 'Failed to save blog');
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      return {
        success: false,
        error: {
          code: 'SAVE_ERROR',
          message: errorMessage
        }
      };
    } finally {
      setIsSaving(false);
    }
  }, []);

  const loadBlog = useCallback(async (id: string): Promise<BlogOperationResult<BlogPost>> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = BlogLocalStorage.loadBlog(id);
      
      if (!result.success) {
        setError(result.error?.message || 'Failed to load blog');
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      return {
        success: false,
        error: {
          code: 'LOAD_ERROR',
          message: errorMessage
        }
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteBlog = useCallback(async (id: string): Promise<BlogOperationResult<void>> => {
    setError(null);

    try {
      const result = BlogLocalStorage.deleteBlog(id);
      
      if (!result.success) {
        setError(result.error?.message || 'Failed to delete blog');
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      return {
        success: false,
        error: {
          code: 'DELETE_ERROR',
          message: errorMessage
        }
      };
    }
  }, []);

  const createNewBlog = useCallback((initialData: Partial<BlogPost> = {}): BlogPost => {
    const now = new Date();
    return {
      id: generateBlogId(),
      title: '',
      content: '',
      excerpt: '',
      author: 'Anonymous',
      createdAt: now,
      updatedAt: now,
      tags: [],
      readingTime: 0,
      status: 'draft',
      slug: '',
      modificationHistory: [],
      ...initialData
    };
  }, []);

  const updateBlogContent = useCallback((blog: BlogPost, content: string): BlogPost => {
    const updatedBlog = {
      ...blog,
      content,
      excerpt: extractExcerpt(content),
      readingTime: calculateReadingTime(content),
      updatedAt: new Date()
    };

    // Update slug if title changed
    if (blog.title && !blog.slug) {
      updatedBlog.slug = generateSlug(blog.title);
    }

    return updatedBlog;
  }, []);

  const updateBlogMetadata = useCallback((blog: BlogPost, metadata: Partial<BlogPost>): BlogPost => {
    const updatedBlog = {
      ...blog,
      ...metadata,
      updatedAt: new Date()
    };

    // Update slug if title changed
    if (metadata.title && metadata.title !== blog.title) {
      updatedBlog.slug = generateSlug(metadata.title);
    }

    // Update excerpt if content changed
    if (metadata.content && metadata.content !== blog.content) {
      updatedBlog.excerpt = extractExcerpt(metadata.content);
      updatedBlog.readingTime = calculateReadingTime(metadata.content);
    }

    return updatedBlog;
  }, []);

  const addModificationEntry = useCallback((blog: BlogPost, description: string, author: string): BlogPost => {
    const entry: ModificationEntry = {
      id: `mod_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      description,
      author
    };

    return {
      ...blog,
      modificationHistory: [entry, ...blog.modificationHistory],
      updatedAt: new Date()
    };
  }, []);

  const enableAutoSave = useCallback((blog: BlogPost, interval?: number) => {
    disableAutoSave(); // Clear any existing timer
    
    autoSaveBlogRef.current = blog;
    const saveInterval = interval || options.autoSaveInterval || BLOG_STORAGE_CONFIG.AUTO_SAVE_INTERVAL;
    
    autoSaveTimerRef.current = setInterval(async () => {
      if (autoSaveBlogRef.current) {
        await saveBlog(autoSaveBlogRef.current);
      }
    }, saveInterval);
  }, [options.autoSaveInterval, saveBlog]);

  const disableAutoSave = useCallback(() => {
    if (autoSaveTimerRef.current) {
      clearInterval(autoSaveTimerRef.current);
      autoSaveTimerRef.current = null;
    }
    autoSaveBlogRef.current = null;
  }, []);

  return {
    // State
    isSaving,
    isLoading,
    lastSaved,
    error,
    
    // Actions
    saveBlog,
    loadBlog,
    deleteBlog,
    createNewBlog,
    updateBlogContent,
    updateBlogMetadata,
    addModificationEntry,
    
    // Auto-save
    enableAutoSave,
    disableAutoSave,
    
    // Utilities
    clearError
  };
};
