import { useState, useEffect, useCallback } from "react";

/**
 * Enhanced localStorage hook with better error handling and serialization
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  // Get value from localStorage or use initial value
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      if (typeof window === "undefined") {
        return initialValue;
      }

      const item = window.localStorage.getItem(key);
      if (!item) {
        return initialValue;
      }

      // Try to parse as JSON first, if it fails, return the raw string
      try {
        return JSON.parse(item);
      } catch {
        // If JSON parsing fails, check if the stored value is a string that matches our expected type
        if (typeof initialValue === 'string') {
          return item as T;
        }
        // If not a string type, return initial value
        return initialValue;
      }
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Set value in localStorage and state
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);

        if (typeof window !== "undefined") {
          // Store strings directly, objects as JSON
          const stringValue = typeof valueToStore === 'string' 
            ? valueToStore 
            : JSON.stringify(valueToStore);
          window.localStorage.setItem(key, stringValue);
        }
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  // Remove value from localStorage
  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      if (typeof window !== "undefined") {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  // Listen for changes in other tabs/windows
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          // Try to parse as JSON first, if it fails, use the raw string
          try {
            setStoredValue(JSON.parse(e.newValue));
          } catch {
            if (typeof initialValue === 'string') {
              setStoredValue(e.newValue as T);
            }
          }
        } catch (error) {
          console.warn(`Error parsing localStorage value for key "${key}":`, error);
        }
      }
    };

    if (typeof window !== "undefined") {
      window.addEventListener("storage", handleStorageChange);
      return () => window.removeEventListener("storage", handleStorageChange);
    }
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue];
}

export default useLocalStorage;
