import { useState, useEffect, CSSProperties, useMemo } from 'react';
import { ScrollAnimationOptions, ScrollAnimationResult } from '../types/common';
import { useIntersectionObserver } from './useIntersectionObserver';

// Enhanced scroll animation hook return type
interface ScrollAnimationReturn extends ScrollAnimationResult {
  animationClass: string;
}

/**
 * Enhanced scroll animation hook with better performance and flexibility
 * @param {ScrollAnimationOptions} options - Animation options
 * @returns {ScrollAnimationReturn} - Element ref, visibility state, and styles
 */
const useScrollAnimation = (options: ScrollAnimationOptions): ScrollAnimationReturn => {
  const {
    animation = 'fade-in',
    threshold = 0.1,
    delay = 0,
    duration = 600,
    once = true
  } = options;

  const [isAnimating, setIsAnimating] = useState(false);
  
  const { elementRef, isIntersecting, hasIntersected } = useIntersectionObserver({
    threshold,
    triggerOnce: once,
    rootMargin: '0px 0px -50px 0px'
  });

  // Handle animation trigger with delay
  useEffect(() => {
    if (isIntersecting || (hasIntersected && once)) {
      const timer = setTimeout(() => {
        setIsAnimating(true);
      }, delay);

      return () => clearTimeout(timer);
    } else if (!once && !isIntersecting) {
      setIsAnimating(false);
    }
  }, [isIntersecting, hasIntersected, delay, once]);

  // Memoized animation styles for better performance
  const animationStyle = useMemo((): CSSProperties => {
    const baseStyle: CSSProperties = {
      transition: `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`,
      willChange: 'transform, opacity',
    };

    if (!isAnimating) {
      const hiddenStyles: Record<string, CSSProperties> = {
        'fade-up': {
          opacity: 0,
          transform: 'translateY(30px)',
        },
        'fade-down': {
          opacity: 0,
          transform: 'translateY(-30px)',
        },
        'fade-left': {
          opacity: 0,
          transform: 'translateX(30px)',
        },
        'fade-right': {
          opacity: 0,
          transform: 'translateX(-30px)',
        },
        'zoom-in': {
          opacity: 0,
          transform: 'scale(0.8)',
        },
        'zoom-out': {
          opacity: 0,
          transform: 'scale(1.2)',
        },
        'flip': {
          opacity: 0,
          transform: 'rotateY(90deg)',
        },
        'fade-in': {
          opacity: 0,
        },
      };

      return {
        ...baseStyle,
        ...hiddenStyles[animation],
      };
    }

    // Visible state - reset all transforms
    return {
      ...baseStyle,
      opacity: 1,
      transform: 'translateY(0) translateX(0) scale(1) rotateY(0)',
      willChange: 'auto', // Remove will-change after animation
    };
  }, [isAnimating, animation, duration]);

  return {
    elementRef: elementRef as React.RefObject<HTMLElement>,
    isVisible: isAnimating,
    animationClass: isAnimating ? animation : '',
    style: animationStyle,
  };
};

export default useScrollAnimation;
