// Custom Hooks Library
export { default as useBreakpoint } from './useBreakpoint';
export { default as useHeaderScroll } from './useHeaderScroll';
export { default as useIntersectionObserver } from './useIntersectionObserver';
export { default as useLocalStorage } from './useLocalStorage';
export { default as useParallax } from './useParallax';
export { default as useScrollAnimation } from './useScrollAnimation';
export { default as useThemeSwitch } from './useThemeSwitch';

// Blog Hooks
export { useBlogStorage } from './useBlogStorage';
export { useBlogList } from './useBlogList';
export { useBlogPost } from './useBlogPost';

// Re-export types
export type { ThemeState } from './useThemeSwitch';
export type {
  UseBlogStorageOptions,
  UseBlogStorageReturn
} from './useBlogStorage';
export type {
  UseBlogListOptions,
  UseBlogListReturn
} from './useBlogList';
export type {
  UseBlogPostOptions,
  UseBlogPostReturn
} from './useBlogPost';