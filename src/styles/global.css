/* Enhanced Global Styles */
@import './variables.css';
@import './animations.css';

/* Modern CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  transition: background-color var(--transition-normal) var(--ease-custom),
              color var(--transition-normal) var(--ease-custom);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  color: var(--color-heading);
  line-height: var(--line-height-tight);
  margin-bottom: 1rem;
  transition: color var(--transition-normal) var(--ease-custom);
}

p {
  margin-bottom: 1rem;
}

a {
  color: var(--color-link);
  text-decoration: none;
  transition: color var(--transition-normal) var(--ease-custom);
}

a:hover {
  color: var(--color-link-hover);
}

code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background-color: var(--color-muted);
  padding: 0.125rem 0.25rem;
  border-radius: var(--border-radius-sm);
}

/* Form Elements */
button {
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
}

input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

/* Lists */
ul, ol {
  list-style: none;
}

/* Media */
img, video {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-muted);
  border-radius: var(--border-radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: var(--border-radius-full);
  transition: background-color var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-700);
}

/* Dark theme scrollbar */
.theme-dark ::-webkit-scrollbar-track {
  background: var(--color-gray-800);
}

/* Focus Management */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --color-border: var(--color-black);
  }
  
  .theme-dark {
    --color-border: var(--color-white);
  }
}

/* Print Styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a, a:visited {
    text-decoration: underline;
  }
  
  img {
    max-width: 100% !important;
  }
  
  @page {
    margin: 0.5in;
  }
}

/* Layout Components */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--color-background);
  color: var(--color-text);
  overflow-x: hidden;
  transition: background-color var(--transition-normal) var(--ease-custom),
              color var(--transition-normal) var(--ease-custom);
}

main {
  flex: 1;
  display: flex;
  flex-direction: column;
}
