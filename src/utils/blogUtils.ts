import { BlogListItem } from '../types/common';

// Social sharing utilities
export const shareOnTwitter = (title: string, url: string, hashtags?: string[]) => {
  const text = encodeURIComponent(title);
  const shareUrl = encodeURIComponent(url);
  const hashtagString = hashtags ? hashtags.map(tag => `#${tag}`).join(' ') : '';
  const twitterUrl = `https://twitter.com/intent/tweet?text=${text}&url=${shareUrl}&hashtags=${encodeURIComponent(hashtagString)}`;
  window.open(twitterUrl, '_blank', 'width=600,height=400');
};

export const shareOnFacebook = (url: string) => {
  const shareUrl = encodeURIComponent(url);
  const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`;
  window.open(facebookUrl, '_blank', 'width=600,height=400');
};

export const shareOnLinkedIn = (title: string, url: string, summary?: string) => {
  const shareUrl = encodeURIComponent(url);
  const shareTitle = encodeURIComponent(title);
  const shareSummary = summary ? encodeURIComponent(summary) : '';
  const linkedInUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${shareUrl}&title=${shareTitle}&summary=${shareSummary}`;
  window.open(linkedInUrl, '_blank', 'width=600,height=400');
};

export const shareOnReddit = (title: string, url: string) => {
  const shareUrl = encodeURIComponent(url);
  const shareTitle = encodeURIComponent(title);
  const redditUrl = `https://reddit.com/submit?url=${shareUrl}&title=${shareTitle}`;
  window.open(redditUrl, '_blank', 'width=600,height=400');
};

export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch (fallbackErr) {
      document.body.removeChild(textArea);
      return false;
    }
  }
};

// Native sharing API
export const shareNatively = async (title: string, text: string, url: string): Promise<boolean> => {
  if (navigator.share) {
    try {
      await navigator.share({ title, text, url });
      return true;
    } catch (err) {
      console.log('Native sharing cancelled or failed:', err);
      return false;
    }
  }
  return false;
};

// Blog search utilities
export const searchBlogs = (blogs: BlogListItem[], query: string): BlogListItem[] => {
  if (!query.trim()) return blogs;
  
  const searchTerm = query.toLowerCase();
  return blogs.filter(blog => 
    blog.title.toLowerCase().includes(searchTerm) ||
    blog.excerpt.toLowerCase().includes(searchTerm) ||
    blog.author.toLowerCase().includes(searchTerm) ||
    blog.category?.toLowerCase().includes(searchTerm) ||
    blog.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
};

// Blog filtering utilities
export const filterBlogsByCategory = (blogs: BlogListItem[], category: string): BlogListItem[] => {
  if (!category) return blogs;
  return blogs.filter(blog => blog.category === category);
};

export const filterBlogsByTag = (blogs: BlogListItem[], tag: string): BlogListItem[] => {
  if (!tag) return blogs;
  return blogs.filter(blog => blog.tags.includes(tag));
};

export const filterBlogsByAuthor = (blogs: BlogListItem[], author: string): BlogListItem[] => {
  if (!author) return blogs;
  return blogs.filter(blog => blog.author === author);
};

export const filterBlogsByStatus = (blogs: BlogListItem[], status: 'draft' | 'published' | 'archived'): BlogListItem[] => {
  return blogs.filter(blog => blog.status === status);
};

export const filterBlogsByDateRange = (blogs: BlogListItem[], startDate: Date, endDate: Date): BlogListItem[] => {
  return blogs.filter(blog => {
    const blogDate = new Date(blog.createdAt);
    return blogDate >= startDate && blogDate <= endDate;
  });
};

// Blog sorting utilities
export const sortBlogsByDate = (blogs: BlogListItem[], order: 'asc' | 'desc' = 'desc'): BlogListItem[] => {
  return [...blogs].sort((a, b) => {
    const dateA = new Date(a.publishedAt || a.createdAt).getTime();
    const dateB = new Date(b.publishedAt || b.createdAt).getTime();
    return order === 'desc' ? dateB - dateA : dateA - dateB;
  });
};

export const sortBlogsByTitle = (blogs: BlogListItem[], order: 'asc' | 'desc' = 'asc'): BlogListItem[] => {
  return [...blogs].sort((a, b) => {
    const comparison = a.title.localeCompare(b.title);
    return order === 'desc' ? -comparison : comparison;
  });
};

export const sortBlogsByReadingTime = (blogs: BlogListItem[], order: 'asc' | 'desc' = 'asc'): BlogListItem[] => {
  return [...blogs].sort((a, b) => {
    return order === 'desc' ? b.readingTime - a.readingTime : a.readingTime - b.readingTime;
  });
};

// Related posts utilities
export const getRelatedBlogs = (
  currentBlog: BlogListItem, 
  allBlogs: BlogListItem[], 
  maxResults: number = 3
): BlogListItem[] => {
  const relatedBlogs = allBlogs
    .filter(blog => 
      blog.id !== currentBlog.id && 
      blog.status === 'published'
    )
    .map(blog => {
      let score = 0;
      
      // Same category gets higher score
      if (blog.category === currentBlog.category) {
        score += 3;
      }
      
      // Shared tags get points
      const sharedTags = blog.tags.filter(tag => currentBlog.tags.includes(tag));
      score += sharedTags.length * 2;
      
      // Same author gets a point
      if (blog.author === currentBlog.author) {
        score += 1;
      }
      
      return { blog, score };
    })
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, maxResults)
    .map(item => item.blog);
    
  return relatedBlogs;
};

// Blog analytics utilities
export const getBlogStats = (blogs: BlogListItem[]) => {
  const totalBlogs = blogs.length;
  const publishedBlogs = blogs.filter(blog => blog.status === 'published').length;
  const draftBlogs = blogs.filter(blog => blog.status === 'draft').length;
  const archivedBlogs = blogs.filter(blog => blog.status === 'archived').length;
  
  const totalReadingTime = blogs.reduce((total, blog) => total + blog.readingTime, 0);
  const averageReadingTime = totalBlogs > 0 ? Math.round(totalReadingTime / totalBlogs) : 0;
  
  const categories = Array.from(new Set(blogs.map(blog => blog.category).filter(Boolean)));
  const tags = Array.from(new Set(blogs.flatMap(blog => blog.tags)));
  const authors = Array.from(new Set(blogs.map(blog => blog.author)));
  
  const categoryStats = categories.map(category => ({
    category,
    count: blogs.filter(blog => blog.category === category).length
  }));
  
  const tagStats = tags.map(tag => ({
    tag,
    count: blogs.filter(blog => blog.tags.includes(tag)).length
  })).sort((a, b) => b.count - a.count);
  
  const authorStats = authors.map(author => ({
    author,
    count: blogs.filter(blog => blog.author === author).length,
    totalReadingTime: blogs
      .filter(blog => blog.author === author)
      .reduce((total, blog) => total + blog.readingTime, 0)
  }));
  
  return {
    totalBlogs,
    publishedBlogs,
    draftBlogs,
    archivedBlogs,
    totalReadingTime,
    averageReadingTime,
    categories: categoryStats,
    tags: tagStats,
    authors: authorStats
  };
};

// URL utilities
export const generateBlogUrl = (slug: string, baseUrl?: string): string => {
  const base = baseUrl || window.location.origin;
  return `${base}/blog/${slug}`;
};

export const generateBlogEditUrl = (blogId: string, baseUrl?: string): string => {
  const base = baseUrl || window.location.origin;
  return `${base}/blog-editor/${blogId}`;
};

// Date utilities
export const formatBlogDate = (date: Date, format: 'short' | 'long' | 'relative' = 'long'): string => {
  const blogDate = new Date(date);
  
  switch (format) {
    case 'short':
      return blogDate.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    case 'long':
      return blogDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    case 'relative':
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - blogDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 1) return 'Yesterday';
      if (diffDays < 7) return `${diffDays} days ago`;
      if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
      if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`;
      return `${Math.ceil(diffDays / 365)} years ago`;
    default:
      return blogDate.toLocaleDateString();
  }
};

// Validation utilities
export const validateBlogSlug = (slug: string): boolean => {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
};

export const validateBlogUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};
