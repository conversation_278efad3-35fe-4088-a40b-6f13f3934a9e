// Performance monitoring utilities

export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage?: number;
}

// Measure component render time
export const measureRenderTime = (componentName: string) => {
  const startTime = performance.now();
  
  return () => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`);
    }
    
    return renderTime;
  };
};

// Debounce function for performance optimization
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Throttle function for performance optimization
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Lazy load images with intersection observer
export const lazyLoadImage = (
  img: HTMLImageElement,
  src: string,
  options?: IntersectionObserverInit
) => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const target = entry.target as HTMLImageElement;
        target.src = src;
        target.classList.remove('lazy');
        observer.unobserve(target);
      }
    });
  }, options);
  
  observer.observe(img);
  return observer;
};

// Preload critical resources
export const preloadResource = (href: string, as: string) => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  document.head.appendChild(link);
};

// Memory usage monitoring
export const getMemoryUsage = (): number | undefined => {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return memory.usedJSHeapSize / 1024 / 1024; // Convert to MB
  }
  return undefined;
};

// Web Vitals monitoring
export const measureWebVitals = () => {
  // Largest Contentful Paint
  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    const lastEntry = entries[entries.length - 1];
    
    if (process.env.NODE_ENV === 'development') {
      console.log('LCP:', lastEntry.startTime);
    }
  });
  
  observer.observe({ entryTypes: ['largest-contentful-paint'] });
  
  // First Input Delay
  const fidObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    entries.forEach(entry => {
      if (process.env.NODE_ENV === 'development') {
        // console.log('FID:', entry.processingStart - entry.startTime);
      }
    });
  });
  
  fidObserver.observe({ entryTypes: ['first-input'] });
};

// Bundle size analyzer (development only)
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    const scripts = document.querySelectorAll('script[src]');
    let totalSize = 0;
    
    scripts.forEach(script => {
      const src = (script as HTMLScriptElement).src;
      if (src.includes('static/js/')) {
        fetch(src, { method: 'HEAD' })
          .then(response => {
            const size = response.headers.get('content-length');
            if (size) {
              totalSize += parseInt(size);
              console.log(`Bundle size: ${(totalSize / 1024).toFixed(2)} KB`);
            }
          })
          .catch(() => {
            // Ignore errors in development
          });
      }
    });
  }
};

// Performance budget checker
export const checkPerformanceBudget = (metrics: PerformanceMetrics) => {
  const budgets = {
    loadTime: 3000, // 3 seconds
    renderTime: 16, // 16ms for 60fps
    interactionTime: 100, // 100ms
    memoryUsage: 50 // 50MB
  };
  
  const violations: string[] = [];
  
  if (metrics.loadTime > budgets.loadTime) {
    violations.push(`Load time exceeded: ${metrics.loadTime}ms > ${budgets.loadTime}ms`);
  }
  
  if (metrics.renderTime > budgets.renderTime) {
    violations.push(`Render time exceeded: ${metrics.renderTime}ms > ${budgets.renderTime}ms`);
  }
  
  if (metrics.interactionTime > budgets.interactionTime) {
    violations.push(`Interaction time exceeded: ${metrics.interactionTime}ms > ${budgets.interactionTime}ms`);
  }
  
  if (metrics.memoryUsage && metrics.memoryUsage > budgets.memoryUsage) {
    violations.push(`Memory usage exceeded: ${metrics.memoryUsage}MB > ${budgets.memoryUsage}MB`);
  }
  
  if (violations.length > 0 && process.env.NODE_ENV === 'development') {
    console.warn('Performance budget violations:', violations);
  }
  
  return violations;
};
