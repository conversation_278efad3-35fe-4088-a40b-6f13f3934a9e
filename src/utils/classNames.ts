/**
 * Utility function to conditionally join class names
 * Alternative to clsx for simple use cases
 */
export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

/**
 * Create CSS class variants based on props
 */
export const createVariants = <T extends Record<string, any>>(
  baseClass: string,
  variants: T,
  props: Partial<keyof T>
): string => {
  const variantClasses = Object.entries(props)
    .filter(([, value]) => value)
    .map(([key]) => variants[key])
    .filter(Boolean);
  
  return cn(baseClass, ...variantClasses);
};

/**
 * Generate responsive class names
 */
export const responsive = (
  base: string,
  breakpoints: Record<string, string>
): string => {
  const classes = [base];
  
  Object.entries(breakpoints).forEach(([breakpoint, value]) => {
    if (value) {
      classes.push(`${breakpoint}:${value}`);
    }
  });
  
  return classes.join(' ');
};

export default cn;