/**
 * Utility functions for localStorage management
 */

/**
 * Clear invalid localStorage entries that might cause JSON parsing errors
 */
export const clearInvalidLocalStorage = (): void => {
  if (typeof window === 'undefined') return;

  const keysToCheck = ['theme'];
  
  keysToCheck.forEach(key => {
    try {
      const item = window.localStorage.getItem(key);
      if (item) {
        // Try to parse as JSON
        JSON.parse(item);
      }
    } catch (error) {
      // If parsing fails, remove the invalid entry
      console.warn(`Clearing invalid localStorage key "${key}":`, error);
      window.localStorage.removeItem(key);
    }
  });
};

/**
 * Migrate old theme storage format to new format
 */
export const migrateThemeStorage = (): void => {
  if (typeof window === 'undefined') return;

  try {
    const theme = window.localStorage.getItem('theme');
    if (theme && (theme === 'light' || theme === 'dark')) {
      // Theme is already in the correct format (plain string)
      return;
    }
    
    // If theme exists but is not a valid value, remove it
    if (theme && theme !== 'light' && theme !== 'dark') {
      window.localStorage.removeItem('theme');
    }
  } catch (error) {
    console.warn('Error migrating theme storage:', error);
    window.localStorage.removeItem('theme');
  }
};

/**
 * Initialize localStorage cleanup on app start
 */
export const initializeLocalStorage = (): void => {
  clearInvalidLocalStorage();
  migrateThemeStorage();
};