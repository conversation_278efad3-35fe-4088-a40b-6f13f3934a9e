import { BlogPost, BlogListItem, BlogMetadata, BlogOperationResult, BlogError } from '../types/common';

// Blog storage configuration
export const BLOG_STORAGE_CONFIG = {
  STORAGE_KEY: 'blog_posts',
  METADATA_KEY: 'blog_metadata',
  SETTINGS_KEY: 'blog_settings',
  MAX_BLOGS: 1000,
  AUTO_SAVE_INTERVAL: 30000, // 30 seconds
  BACKUP_INTERVAL: 300000, // 5 minutes
} as const;

// Generate unique blog ID
export const generateBlogId = (): string => {
  return `blog_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Generate blog slug from title
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

// Calculate reading time based on content
export const calculateReadingTime = (content: string): number => {
  const wordsPerMinute = 200;
  const words = content.trim().split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
};

// Extract excerpt from content
export const extractExcerpt = (content: string, maxLength: number = 150): string => {
  // Remove markdown formatting for excerpt
  const plainText = content
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links, keep text
    .replace(/!\[.*?\]\(.*?\)/g, '') // Remove images
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .trim();

  if (plainText.length <= maxLength) {
    return plainText;
  }

  // Find the last complete word within the limit
  const truncated = plainText.substr(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');
  
  if (lastSpaceIndex > 0) {
    return truncated.substr(0, lastSpaceIndex) + '...';
  }
  
  return truncated + '...';
};

// Validate blog post data
export const validateBlogPost = (blog: Partial<BlogPost>): BlogOperationResult<BlogPost> => {
  const errors: string[] = [];

  if (!blog.title || blog.title.trim().length === 0) {
    errors.push('Title is required');
  }

  if (!blog.content || blog.content.trim().length === 0) {
    errors.push('Content is required');
  }

  if (!blog.author || blog.author.trim().length === 0) {
    errors.push('Author is required');
  }

  if (blog.title && blog.title.length > 200) {
    errors.push('Title must be less than 200 characters');
  }

  if (blog.excerpt && blog.excerpt.length > 300) {
    errors.push('Excerpt must be less than 300 characters');
  }

  if (errors.length > 0) {
    return {
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Blog post validation failed',
        details: errors
      }
    };
  }

  return { success: true };
};

// Create blog metadata from blog post
export const createBlogMetadata = (blog: BlogPost): BlogMetadata => {
  return {
    id: blog.id,
    title: blog.title,
    excerpt: blog.excerpt,
    author: blog.author,
    createdAt: blog.createdAt,
    updatedAt: blog.updatedAt,
    publishedAt: blog.publishedAt,
    tags: blog.tags,
    category: blog.category,
    thumbnail: blog.thumbnail,
    readingTime: blog.readingTime,
    status: blog.status,
    slug: blog.slug
  };
};

// Convert blog post to list item
export const blogToListItem = (blog: BlogPost): BlogListItem => {
  return createBlogMetadata(blog);
};

// Local storage operations
export class BlogLocalStorage {
  private static getStorageKey(key: string): string {
    return `${BLOG_STORAGE_CONFIG.STORAGE_KEY}_${key}`;
  }

  static saveBlog(blog: BlogPost): BlogOperationResult<BlogPost> {
    try {
      const validation = validateBlogPost(blog);
      if (!validation.success) {
        return validation;
      }

      const blogData = JSON.stringify(blog);
      const storageKey = this.getStorageKey(blog.id);
      
      localStorage.setItem(storageKey, blogData);
      
      // Update metadata index
      this.updateMetadataIndex(blog);
      
      return {
        success: true,
        data: blog
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STORAGE_ERROR',
          message: 'Failed to save blog post',
          details: error
        }
      };
    }
  }

  static loadBlog(id: string): BlogOperationResult<BlogPost> {
    try {
      const storageKey = this.getStorageKey(id);
      const blogData = localStorage.getItem(storageKey);
      
      if (!blogData) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Blog post not found'
          }
        };
      }

      const blog = JSON.parse(blogData) as BlogPost;
      
      // Convert date strings back to Date objects
      blog.createdAt = new Date(blog.createdAt);
      blog.updatedAt = new Date(blog.updatedAt);
      if (blog.publishedAt) {
        blog.publishedAt = new Date(blog.publishedAt);
      }
      
      blog.modificationHistory = blog.modificationHistory.map(entry => ({
        ...entry,
        timestamp: new Date(entry.timestamp)
      }));

      return {
        success: true,
        data: blog
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STORAGE_ERROR',
          message: 'Failed to load blog post',
          details: error
        }
      };
    }
  }

  static deleteBlog(id: string): BlogOperationResult<void> {
    try {
      const storageKey = this.getStorageKey(id);
      localStorage.removeItem(storageKey);
      
      // Remove from metadata index
      this.removeFromMetadataIndex(id);
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STORAGE_ERROR',
          message: 'Failed to delete blog post',
          details: error
        }
      };
    }
  }

  static getAllBlogIds(): string[] {
    const metadata = this.getMetadataIndex();
    return metadata.map(item => item.id);
  }

  static getMetadataIndex(): BlogListItem[] {
    try {
      const metadataJson = localStorage.getItem(BLOG_STORAGE_CONFIG.METADATA_KEY);
      if (!metadataJson) {
        return [];
      }
      
      const metadata = JSON.parse(metadataJson) as BlogListItem[];
      
      // Convert date strings back to Date objects
      return metadata.map(item => ({
        ...item,
        createdAt: new Date(item.createdAt),
        updatedAt: new Date(item.updatedAt),
        publishedAt: item.publishedAt ? new Date(item.publishedAt) : undefined
      }));
    } catch (error) {
      console.error('Failed to load blog metadata index:', error);
      return [];
    }
  }

  private static updateMetadataIndex(blog: BlogPost): void {
    const metadata = this.getMetadataIndex();
    const existingIndex = metadata.findIndex(item => item.id === blog.id);
    const blogListItem = blogToListItem(blog);
    
    if (existingIndex >= 0) {
      metadata[existingIndex] = blogListItem;
    } else {
      metadata.push(blogListItem);
    }
    
    // Sort by updatedAt descending
    metadata.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
    
    localStorage.setItem(BLOG_STORAGE_CONFIG.METADATA_KEY, JSON.stringify(metadata));
  }

  private static removeFromMetadataIndex(id: string): void {
    const metadata = this.getMetadataIndex();
    const filteredMetadata = metadata.filter(item => item.id !== id);
    localStorage.setItem(BLOG_STORAGE_CONFIG.METADATA_KEY, JSON.stringify(filteredMetadata));
  }

  static clearAllBlogs(): BlogOperationResult<void> {
    try {
      const blogIds = this.getAllBlogIds();
      
      // Remove all blog posts
      blogIds.forEach(id => {
        const storageKey = this.getStorageKey(id);
        localStorage.removeItem(storageKey);
      });
      
      // Clear metadata index
      localStorage.removeItem(BLOG_STORAGE_CONFIG.METADATA_KEY);
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'STORAGE_ERROR',
          message: 'Failed to clear all blogs',
          details: error
        }
      };
    }
  }
}
