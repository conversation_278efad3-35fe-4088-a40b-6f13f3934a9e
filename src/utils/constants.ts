// Application Constants
export const APP_CONFIG = {
  name: 'Personal Website',
  version: '2.0.0',
  author: 'Your Name',
  description: 'Modern personal website built with React and TypeScript',
} as const;

// Animation Constants
export const ANIMATION_DURATION = {
  fast: 150,
  normal: 300,
  slow: 500,
} as const;

// Breakpoint Constants
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

// Z-Index Constants
export const Z_INDEX = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal: 1040,
  popover: 1050,
  tooltip: 1060,
  toast: 1070,
} as const;

// Common Sizes
export const SIZES = {
  xs: 'xs',
  sm: 'sm',
  md: 'md',
  lg: 'lg',
  xl: 'xl',
} as const;

// Theme Constants
export const THEME_STORAGE_KEY = 'theme';
export const DEFAULT_THEME = 'light' as const;

export type Size = keyof typeof SIZES;