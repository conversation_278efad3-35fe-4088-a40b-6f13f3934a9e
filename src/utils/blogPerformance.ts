import { BlogListItem, BlogPost } from '../types/common';

// Cache implementation for blog data
class BlogCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private defaultTTL = 5 * 60 * 1000; // 5 minutes

  set(key: string, data: any, ttl?: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;

    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // Clean expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of Array.from(this.cache.entries())) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Global blog cache instance
export const blogCache = new BlogCache();

// Debounce utility for search and filtering
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle utility for scroll events
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Lazy loading utility for blog images
export const createImageLoader = () => {
  const imageCache = new Set<string>();

  const preloadImage = (src: string): Promise<void> => {
    if (imageCache.has(src)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        imageCache.add(src);
        resolve();
      };
      img.onerror = reject;
      img.src = src;
    });
  };

  return {
    preloadImage,

    preloadImages: async (srcs: string[]): Promise<void> => {
      const promises = srcs
        .filter(src => !imageCache.has(src))
        .map(src => preloadImage(src));

      await Promise.allSettled(promises);
    },

    isImageCached: (src: string): boolean => imageCache.has(src)
  };
};

// Virtual scrolling utility for large blog lists
export class VirtualScrollManager {
  private itemHeight: number;
  private containerHeight: number;
  private scrollTop: number = 0;
  private totalItems: number = 0;
  private visibleCount: number = 0;
  private bufferSize: number = 5;

  constructor(itemHeight: number, containerHeight: number) {
    this.itemHeight = itemHeight;
    this.containerHeight = containerHeight;
    this.visibleCount = Math.ceil(containerHeight / itemHeight);
  }

  updateScroll(scrollTop: number): void {
    this.scrollTop = scrollTop;
  }

  updateTotalItems(count: number): void {
    this.totalItems = count;
  }

  getVisibleRange(): { start: number; end: number } {
    const start = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.bufferSize);
    const end = Math.min(
      this.totalItems,
      start + this.visibleCount + this.bufferSize * 2
    );
    
    return { start, end };
  }

  getTotalHeight(): number {
    return this.totalItems * this.itemHeight;
  }

  getOffsetY(): number {
    const { start } = this.getVisibleRange();
    return start * this.itemHeight;
  }
}

// Pagination utility with caching
export class PaginatedBlogManager {
  private pageSize: number;
  private cache = new Map<string, BlogListItem[]>();

  constructor(pageSize: number = 10) {
    this.pageSize = pageSize;
  }

  private getCacheKey(page: number, filters: any): string {
    return `page_${page}_${JSON.stringify(filters)}`;
  }

  getPage(
    allBlogs: BlogListItem[],
    page: number,
    filters: any = {}
  ): { blogs: BlogListItem[]; totalPages: number; hasMore: boolean } {
    const cacheKey = this.getCacheKey(page, filters);
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      const cachedBlogs = this.cache.get(cacheKey)!;
      return {
        blogs: cachedBlogs,
        totalPages: Math.ceil(allBlogs.length / this.pageSize),
        hasMore: page * this.pageSize < allBlogs.length
      };
    }

    // Calculate pagination
    const startIndex = (page - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    const blogs = allBlogs.slice(startIndex, endIndex);
    
    // Cache the result
    this.cache.set(cacheKey, blogs);
    
    return {
      blogs,
      totalPages: Math.ceil(allBlogs.length / this.pageSize),
      hasMore: endIndex < allBlogs.length
    };
  }

  clearCache(): void {
    this.cache.clear();
  }

  preloadNextPage(
    allBlogs: BlogListItem[],
    currentPage: number,
    filters: any = {}
  ): void {
    const nextPage = currentPage + 1;
    const totalPages = Math.ceil(allBlogs.length / this.pageSize);
    
    if (nextPage <= totalPages) {
      // Preload next page in background
      setTimeout(() => {
        this.getPage(allBlogs, nextPage, filters);
      }, 100);
    }
  }
}

// Performance monitoring utilities
export const performanceMonitor = {
  measureTime: <T>(name: string, fn: () => T): T => {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
  },

  measureAsyncTime: async <T>(name: string, fn: () => Promise<T>): Promise<T> => {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
  },

  trackMemoryUsage: (): void => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.log('Memory usage:', {
        used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
        total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB'
      });
    }
  }
};

// Intersection Observer utility for lazy loading
export const createIntersectionObserver = (
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver => {
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  };

  return new IntersectionObserver(callback, defaultOptions);
};

// Optimized search with indexing
export class BlogSearchIndex {
  private index = new Map<string, Set<string>>();
  private blogs = new Map<string, BlogListItem>();

  buildIndex(blogs: BlogListItem[]): void {
    this.index.clear();
    this.blogs.clear();

    blogs.forEach(blog => {
      this.blogs.set(blog.id, blog);
      
      // Index title words
      this.indexText(blog.title, blog.id);
      
      // Index excerpt words
      this.indexText(blog.excerpt, blog.id);
      
      // Index author
      this.indexText(blog.author, blog.id);
      
      // Index category
      if (blog.category) {
        this.indexText(blog.category, blog.id);
      }
      
      // Index tags
      blog.tags.forEach(tag => this.indexText(tag, blog.id));
    });
  }

  private indexText(text: string, blogId: string): void {
    const words = text.toLowerCase().split(/\s+/).filter(word => word.length > 2);
    
    words.forEach(word => {
      if (!this.index.has(word)) {
        this.index.set(word, new Set());
      }
      this.index.get(word)!.add(blogId);
    });
  }

  search(query: string): BlogListItem[] {
    if (!query.trim()) return Array.from(this.blogs.values());

    const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 0);
    let resultIds: Set<string> | null = null;

    searchTerms.forEach(term => {
      const matchingIds = new Set<string>();

      // Find exact matches and partial matches
      for (const [indexedWord, blogIds] of Array.from(this.index.entries())) {
        if (indexedWord.includes(term)) {
          blogIds.forEach((id: string) => matchingIds.add(id));
        }
      }

      if (resultIds === null) {
        resultIds = matchingIds;
      } else {
        // Intersection of results (AND operation)
        resultIds = new Set(Array.from(resultIds).filter(id => matchingIds.has(id)));
      }
    });

    if (!resultIds) return [];

    const results: BlogListItem[] = [];
    (Array.from(resultIds) as string[]).forEach((id: string) => {
      const blog = this.blogs.get(id);
      if (blog) {
        results.push(blog);
      }
    });

    return results;
  }
}

// Cleanup function for performance optimization
export const cleanup = (): void => {
  blogCache.cleanup();
  performanceMonitor.trackMemoryUsage();
};
