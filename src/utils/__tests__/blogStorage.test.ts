import {
  generateBlogId,
  generateSlug,
  calculateReadingTime,
  extractExcerpt,
  validateBlogPost,
  BlogLocalStorage
} from '../blogStorage';
import { BlogPost } from '../../types/common';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('Blog Storage Utilities', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  describe('generateBlogId', () => {
    it('should generate a unique blog ID', () => {
      const id1 = generateBlogId();
      const id2 = generateBlogId();
      
      expect(id1).toMatch(/^blog_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^blog_\d+_[a-z0-9]+$/);
      expect(id1).not.toBe(id2);
    });
  });

  describe('generateSlug', () => {
    it('should generate a valid slug from title', () => {
      expect(generateSlug('Hello World')).toBe('hello-world');
      expect(generateSlug('React & TypeScript Guide')).toBe('react-typescript-guide');
      expect(generateSlug('  Multiple   Spaces  ')).toBe('multiple-spaces');
      expect(generateSlug('Special!@#$%Characters')).toBe('specialcharacters');
    });

    it('should handle empty strings', () => {
      expect(generateSlug('')).toBe('');
      expect(generateSlug('   ')).toBe('');
    });
  });

  describe('calculateReadingTime', () => {
    it('should calculate reading time correctly', () => {
      const shortText = 'This is a short text with about ten words.';
      const longText = Array(200).fill('word').join(' '); // 200 words
      
      expect(calculateReadingTime(shortText)).toBe(1); // Minimum 1 minute
      expect(calculateReadingTime(longText)).toBe(1); // 200 words = 1 minute
      expect(calculateReadingTime(Array(400).fill('word').join(' '))).toBe(2); // 400 words = 2 minutes
    });

    it('should handle empty content', () => {
      expect(calculateReadingTime('')).toBe(1);
      expect(calculateReadingTime('   ')).toBe(1);
    });
  });

  describe('extractExcerpt', () => {
    it('should extract excerpt from markdown content', () => {
      const content = `# Title

This is the first paragraph with some **bold** text and *italic* text.

This is the second paragraph with a [link](https://example.com).

## Another Section

More content here.`;

      const excerpt = extractExcerpt(content, 100);
      expect(excerpt).not.toContain('#');
      expect(excerpt).not.toContain('**');
      expect(excerpt).not.toContain('*');
      expect(excerpt).not.toContain('[');
      expect(excerpt.length).toBeLessThanOrEqual(103); // 100 + '...'
    });

    it('should handle short content', () => {
      const shortContent = 'Short content';
      expect(extractExcerpt(shortContent)).toBe('Short content');
    });

    it('should truncate at word boundaries', () => {
      const content = 'This is a very long sentence that should be truncated at word boundaries';
      const excerpt = extractExcerpt(content, 30);
      expect(excerpt).toMatch(/\.\.\.$|[a-zA-Z]$/); // Should end with ... or a letter
    });
  });

  describe('validateBlogPost', () => {
    const validBlog: Partial<BlogPost> = {
      title: 'Test Blog',
      content: 'This is test content',
      author: 'Test Author'
    };

    it('should validate a valid blog post', () => {
      const result = validateBlogPost(validBlog);
      expect(result.success).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject blog without title', () => {
      const result = validateBlogPost({ ...validBlog, title: '' });
      expect(result.success).toBe(false);
      expect(result.error?.details).toContain('Title is required');
    });

    it('should reject blog without content', () => {
      const result = validateBlogPost({ ...validBlog, content: '' });
      expect(result.success).toBe(false);
      expect(result.error?.details).toContain('Content is required');
    });

    it('should reject blog without author', () => {
      const result = validateBlogPost({ ...validBlog, author: '' });
      expect(result.success).toBe(false);
      expect(result.error?.details).toContain('Author is required');
    });

    it('should reject blog with title too long', () => {
      const longTitle = 'a'.repeat(201);
      const result = validateBlogPost({ ...validBlog, title: longTitle });
      expect(result.success).toBe(false);
      expect(result.error?.details).toContain('Title must be less than 200 characters');
    });

    it('should reject blog with excerpt too long', () => {
      const longExcerpt = 'a'.repeat(301);
      const result = validateBlogPost({ ...validBlog, excerpt: longExcerpt });
      expect(result.success).toBe(false);
      expect(result.error?.details).toContain('Excerpt must be less than 300 characters');
    });
  });

  describe('BlogLocalStorage', () => {
    const testBlog: BlogPost = {
      id: 'test-blog-1',
      title: 'Test Blog',
      content: 'This is test content',
      excerpt: 'Test excerpt',
      author: 'Test Author',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      tags: ['test', 'blog'],
      readingTime: 1,
      status: 'published',
      slug: 'test-blog',
      modificationHistory: []
    };

    it('should save and load a blog post', () => {
      const saveResult = BlogLocalStorage.saveBlog(testBlog);
      expect(saveResult.success).toBe(true);

      const loadResult = BlogLocalStorage.loadBlog(testBlog.id);
      expect(loadResult.success).toBe(true);
      expect(loadResult.data?.title).toBe(testBlog.title);
      expect(loadResult.data?.content).toBe(testBlog.content);
    });

    it('should handle loading non-existent blog', () => {
      const result = BlogLocalStorage.loadBlog('non-existent');
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('NOT_FOUND');
    });

    it('should delete a blog post', () => {
      BlogLocalStorage.saveBlog(testBlog);
      
      const deleteResult = BlogLocalStorage.deleteBlog(testBlog.id);
      expect(deleteResult.success).toBe(true);

      const loadResult = BlogLocalStorage.loadBlog(testBlog.id);
      expect(loadResult.success).toBe(false);
    });

    it('should maintain metadata index', () => {
      BlogLocalStorage.saveBlog(testBlog);
      
      const metadata = BlogLocalStorage.getMetadataIndex();
      expect(metadata).toHaveLength(1);
      expect(metadata[0].id).toBe(testBlog.id);
      expect(metadata[0].title).toBe(testBlog.title);
    });

    it('should clear all blogs', () => {
      BlogLocalStorage.saveBlog(testBlog);
      BlogLocalStorage.saveBlog({ ...testBlog, id: 'test-blog-2' });
      
      const clearResult = BlogLocalStorage.clearAllBlogs();
      expect(clearResult.success).toBe(true);

      const metadata = BlogLocalStorage.getMetadataIndex();
      expect(metadata).toHaveLength(0);
    });

    it('should handle corrupted data gracefully', () => {
      // Simulate corrupted data
      localStorage.setItem('blog_posts_test-blog-1', 'invalid json');
      
      const result = BlogLocalStorage.loadBlog('test-blog-1');
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('STORAGE_ERROR');
    });
  });
});
