import {
  searchBlogs,
  filterBlogsByCategory,
  filterBlogsByTag,
  sortBlogsByDate,
  getRelatedBlogs,
  getBlogStats,
  formatBlogDate,
  validateBlogSlug,
  validateBlogUrl
} from '../blogUtils';
import { BlogListItem } from '../../types/common';

const mockBlogs: BlogListItem[] = [
  {
    id: '1',
    title: 'React Hooks Guide',
    excerpt: 'Learn about React hooks and how to use them effectively',
    author: '<PERSON>',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    publishedAt: new Date('2024-01-01'),
    tags: ['react', 'hooks', 'javascript'],
    category: 'Tutorial',
    readingTime: 5,
    status: 'published',
    slug: 'react-hooks-guide'
  },
  {
    id: '2',
    title: 'TypeScript Best Practices',
    excerpt: 'Best practices for writing TypeScript code',
    author: '<PERSON>',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    publishedAt: new Date('2024-01-02'),
    tags: ['typescript', 'javascript', 'best-practices'],
    category: 'Tutorial',
    readingTime: 8,
    status: 'published',
    slug: 'typescript-best-practices'
  },
  {
    id: '3',
    title: 'CSS Grid Layout',
    excerpt: 'Master CSS Grid for modern web layouts',
    author: 'John Doe',
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
    tags: ['css', 'grid', 'layout'],
    category: 'Design',
    readingTime: 6,
    status: 'draft',
    slug: 'css-grid-layout'
  }
];

describe('Blog Utils', () => {
  describe('searchBlogs', () => {
    it('should search blogs by title', () => {
      const results = searchBlogs(mockBlogs, 'React');
      expect(results).toHaveLength(1);
      expect(results[0].title).toBe('React Hooks Guide');
    });

    it('should search blogs by excerpt', () => {
      const results = searchBlogs(mockBlogs, 'TypeScript code');
      expect(results).toHaveLength(1);
      expect(results[0].title).toBe('TypeScript Best Practices');
    });

    it('should search blogs by author', () => {
      const results = searchBlogs(mockBlogs, 'John Doe');
      expect(results).toHaveLength(2);
    });

    it('should search blogs by tags', () => {
      const results = searchBlogs(mockBlogs, 'javascript');
      expect(results).toHaveLength(2);
    });

    it('should search blogs by category', () => {
      const results = searchBlogs(mockBlogs, 'Tutorial');
      expect(results).toHaveLength(2);
    });

    it('should return all blogs for empty query', () => {
      const results = searchBlogs(mockBlogs, '');
      expect(results).toHaveLength(3);
    });

    it('should be case insensitive', () => {
      const results = searchBlogs(mockBlogs, 'REACT');
      expect(results).toHaveLength(1);
    });
  });

  describe('filterBlogsByCategory', () => {
    it('should filter blogs by category', () => {
      const results = filterBlogsByCategory(mockBlogs, 'Tutorial');
      expect(results).toHaveLength(2);
      expect(results.every(blog => blog.category === 'Tutorial')).toBe(true);
    });

    it('should return all blogs for empty category', () => {
      const results = filterBlogsByCategory(mockBlogs, '');
      expect(results).toHaveLength(3);
    });
  });

  describe('filterBlogsByTag', () => {
    it('should filter blogs by tag', () => {
      const results = filterBlogsByTag(mockBlogs, 'javascript');
      expect(results).toHaveLength(2);
      expect(results.every(blog => blog.tags.includes('javascript'))).toBe(true);
    });

    it('should return all blogs for empty tag', () => {
      const results = filterBlogsByTag(mockBlogs, '');
      expect(results).toHaveLength(3);
    });
  });

  describe('sortBlogsByDate', () => {
    it('should sort blogs by date descending by default', () => {
      const results = sortBlogsByDate(mockBlogs);
      expect(results[0].createdAt.getTime()).toBeGreaterThan(results[1].createdAt.getTime());
      expect(results[1].createdAt.getTime()).toBeGreaterThan(results[2].createdAt.getTime());
    });

    it('should sort blogs by date ascending', () => {
      const results = sortBlogsByDate(mockBlogs, 'asc');
      expect(results[0].createdAt.getTime()).toBeLessThan(results[1].createdAt.getTime());
      expect(results[1].createdAt.getTime()).toBeLessThan(results[2].createdAt.getTime());
    });
  });

  describe('getRelatedBlogs', () => {
    it('should find related blogs by category', () => {
      const results = getRelatedBlogs(mockBlogs[0], mockBlogs, 2);
      expect(results).toHaveLength(1); // Only one other blog in same category
      expect(results[0].category).toBe(mockBlogs[0].category);
    });

    it('should find related blogs by tags', () => {
      const results = getRelatedBlogs(mockBlogs[0], mockBlogs, 2);
      expect(results.length).toBeGreaterThan(0);
    });

    it('should exclude current blog from results', () => {
      const results = getRelatedBlogs(mockBlogs[0], mockBlogs, 5);
      expect(results.every(blog => blog.id !== mockBlogs[0].id)).toBe(true);
    });

    it('should only include published blogs', () => {
      const results = getRelatedBlogs(mockBlogs[0], mockBlogs, 5);
      expect(results.every(blog => blog.status === 'published')).toBe(true);
    });
  });

  describe('getBlogStats', () => {
    it('should calculate blog statistics correctly', () => {
      const stats = getBlogStats(mockBlogs);
      
      expect(stats.totalBlogs).toBe(3);
      expect(stats.publishedBlogs).toBe(2);
      expect(stats.draftBlogs).toBe(1);
      expect(stats.archivedBlogs).toBe(0);
      expect(stats.totalReadingTime).toBe(19); // 5 + 8 + 6
      expect(stats.averageReadingTime).toBe(6); // 19 / 3 rounded
      expect(stats.categories).toHaveLength(2);
      expect(stats.tags.length).toBeGreaterThan(0);
      expect(stats.authors).toHaveLength(2);
    });

    it('should handle empty blog list', () => {
      const stats = getBlogStats([]);
      
      expect(stats.totalBlogs).toBe(0);
      expect(stats.publishedBlogs).toBe(0);
      expect(stats.averageReadingTime).toBe(0);
      expect(stats.categories).toHaveLength(0);
      expect(stats.tags).toHaveLength(0);
      expect(stats.authors).toHaveLength(0);
    });
  });

  describe('formatBlogDate', () => {
    const testDate = new Date('2024-01-15T10:30:00Z');

    it('should format date in short format', () => {
      const formatted = formatBlogDate(testDate, 'short');
      expect(formatted).toMatch(/Jan 15, 2024/);
    });

    it('should format date in long format', () => {
      const formatted = formatBlogDate(testDate, 'long');
      expect(formatted).toMatch(/Monday, January 15, 2024/);
    });

    it('should format date in relative format', () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      const formatted = formatBlogDate(yesterday, 'relative');
      expect(formatted).toBe('Yesterday');
    });
  });

  describe('validateBlogSlug', () => {
    it('should validate correct slugs', () => {
      expect(validateBlogSlug('hello-world')).toBe(true);
      expect(validateBlogSlug('react-hooks-guide')).toBe(true);
      expect(validateBlogSlug('css-grid-2024')).toBe(true);
    });

    it('should reject invalid slugs', () => {
      expect(validateBlogSlug('Hello World')).toBe(false); // spaces
      expect(validateBlogSlug('hello_world')).toBe(false); // underscores
      expect(validateBlogSlug('hello--world')).toBe(false); // double hyphens
      expect(validateBlogSlug('-hello-world')).toBe(false); // leading hyphen
      expect(validateBlogSlug('hello-world-')).toBe(false); // trailing hyphen
      expect(validateBlogSlug('HELLO-WORLD')).toBe(false); // uppercase
    });
  });

  describe('validateBlogUrl', () => {
    it('should validate correct URLs', () => {
      expect(validateBlogUrl('https://example.com')).toBe(true);
      expect(validateBlogUrl('http://localhost:3000')).toBe(true);
      expect(validateBlogUrl('https://blog.example.com/post')).toBe(true);
    });

    it('should reject invalid URLs', () => {
      expect(validateBlogUrl('not-a-url')).toBe(false);
      expect(validateBlogUrl('ftp://example.com')).toBe(true); // FTP is valid URL
      expect(validateBlogUrl('')).toBe(false);
      expect(validateBlogUrl('example.com')).toBe(false); // missing protocol
    });
  });
});
