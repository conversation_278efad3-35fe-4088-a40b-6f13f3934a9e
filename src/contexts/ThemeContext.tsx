import React, { createContext, useContext, ReactNode, useEffect } from 'react';
import useThemeSwitch from '../hooks/useThemeSwitch';
import { ThemeMode } from '../types/common';
import { theme } from '../theme';

// Enhanced theme context type
export interface ThemeContextType {
  theme: ThemeMode;
  toggleTheme: () => void;
  setTheme: (theme: ThemeMode) => void;
  isDarkMode: boolean;
  themeConfig: typeof theme;
}

// Create theme context with null initial value
const ThemeContext = createContext<ThemeContextType | null>(null);

// Theme provider props
interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: ThemeMode;
}

/**
 * Enhanced Theme Provider Component
 * Provides theme state and configuration to the entire app
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  defaultTheme = 'light' 
}) => {
  const themeState = useThemeSwitch(defaultTheme);
  
  // Apply theme-specific CSS custom properties
  useEffect(() => {
    const root = document.documentElement;
    const currentTheme = themeState.isDarkMode ? theme.dark : theme.light;
    
    // Apply theme colors as CSS custom properties
    Object.entries(currentTheme.colors).forEach(([key, value]) => {
      const cssVar = `--color-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
      root.style.setProperty(cssVar, value);
    });
    
    // Apply theme-specific shadows
    Object.entries(currentTheme.shadows).forEach(([key, value]) => {
      const cssVar = `--shadow-${key}`;
      root.style.setProperty(cssVar, value);
    });
  }, [themeState.theme, themeState.isDarkMode]);
  
  const contextValue: ThemeContextType = {
    ...themeState,
    themeConfig: theme,
  };
  
  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Custom hook to use theme context
 * @returns {ThemeContextType} Theme state and methods
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === null) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;
