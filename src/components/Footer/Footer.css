.site-footer {
  position: relative;
  padding: 2rem 0 1rem;
  background-color: var(--color-gray-900);
  color: var(--color-gray-300);
  overflow: hidden;
  border-top: 1px solid var(--color-gray-800);
  background-image: 
    url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-size: 50px 50px;
}

.footer-container {
  position: relative;
  z-index: 10;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* 页脚内容区域 */
.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--color-gray-800);
}

/* 品牌区域 */
.footer-branding {
  flex: 1;
  min-width: 200px;
}

.footer-branding .logo {
  display: inline-block;
  margin-bottom: 0.75rem;
}

.footer-branding .logo-text {
  color: var(--color-white);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(to right, var(--color-indigo-400), var(--color-purple-400));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
}

.footer-branding .logo-text:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.footer-tagline {
  font-size: var(--font-size-sm);
  color: var(--color-gray-400);
  margin-bottom: 1rem;
}

/* 导航区域 */
.footer-nav {
  flex: 1;
  min-width: 200px;
}

.footer-heading {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-white);
  margin-bottom: 0.75rem;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.footer-links-horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding: 0;
  margin: 0;
}

.footer-link {
  color: var(--color-gray-400);
  font-size: var(--font-size-sm);
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
  padding: 2px 0;
}

.footer-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(to right, var(--color-indigo-400), var(--color-purple-400));
  transition: width 0.3s ease;
}

.footer-link:hover {
  color: var(--color-white);
  transform: translateY(-2px);
}

.footer-link:hover::after {
  width: 100%;
}

/* 社交媒体区域 */
.footer-social-container {
  flex: 0 0 auto;
}

.footer-social-links {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.footer-social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-gray-800);
  color: var(--color-gray-300);
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.footer-social-link:hover {
  background: linear-gradient(135deg, var(--color-indigo-500), var(--color-purple-500));
  color: var(--color-white);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(79, 70, 229, 0.3);
}

/* 页脚底部区域 */
.footer-bottom {
  text-align: center;
}

.footer-copyright {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }
  
  .footer-branding,
  .footer-nav {
    width: 100%;
  }
  
  .footer-links-horizontal {
    margin-bottom: 0.5rem;
  }
  
  .footer-social-container {
    width: 100%;
  }
  
  .footer-social-links {
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  .footer-links-horizontal {
    flex-direction: column;
    gap: 0.75rem;
  }
}
