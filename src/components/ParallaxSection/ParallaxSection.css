.parallax-section {
  min-height: 100vh;
  position: relative;
  padding: 6rem 0;
  background: linear-gradient(135deg, var(--color-white) 0%, var(--color-slate-50) 100%);
  overflow: hidden;
  background-image: 
    url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1600 900'%3E%3Cpolygon fill='%23f0f9ff' points='957 450 539 900 1396 900'/%3E%3Cpolygon fill='%23e0f2fe' points='957 450 872.9 900 1396 900'/%3E%3Cpolygon fill='%23dbeafe' points='-60 900 398 662 816 900'/%3E%3Cpolygon fill='%23c7d2fe' points='337 900 398 662 816 900'/%3E%3Cpolygon fill='%23e0e7ff' points='1203 546 1552 900 876 900'/%3E%3Cpolygon fill='%23c4b5fd' points='1203 546 1552 900 1162 900'/%3E%3Cpolygon fill='%23f5f3ff' points='641 695 886 900 367 900'/%3E%3Cpolygon fill='%23ede9fe' points='587 900 641 695 886 900'/%3E%3Cpolygon fill='%23faf5ff' points='1710 900 1401 632 1096 900'/%3E%3Cpolygon fill='%23f3e8ff' points='1710 900 1401 632 1365 900'/%3E%3Cpolygon fill='%23f8fafc' points='1210 900 971 687 725 900'/%3E%3Cpolygon fill='%23f1f5f9' points='943 900 1210 900 971 687'/%3E%3C/svg%3E");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.parallax-container {
  position: relative;
  z-index: 10;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
  max-width: 600px;
  margin: 0 auto;
}

/* 视差效果开关 */
.parallax-toggle-container {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
}

.parallax-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-full);
  background: linear-gradient(135deg, var(--color-indigo-500), var(--color-purple-500));
  color: var(--color-white);
  font-weight: var(--font-weight-medium);
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal) var(--ease-custom);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
  position: relative;
  top: -20px; /* 调整按钮位置，使其更高不会挡住内容 */
}

.parallax-toggle:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 25px rgba(79, 70, 229, 0.5);
}

.parallax-toggle-text {
  font-size: var(--font-size-base);
}

.parallax-toggle-slider {
  position: relative;
  width: 40px;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-full);
}

.parallax-toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background-color: var(--color-white);
  border-radius: 50%;
  transition: transform var(--transition-normal) var(--ease-custom);
}

.parallax-toggle.active .parallax-toggle-slider::before {
  transform: translateX(20px);
}

/* 卡片网格 */
.parallax-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2.5rem;
  margin-top: 2rem;
}

/* 调整右侧卡片之间的间距 */
.parallax-cards > div:nth-child(3n),
.parallax-cards > div:nth-child(3n-1) {
  margin-right: 1.5rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .parallax-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .parallax-cards {
    grid-template-columns: 1fr;
  }
  
  .section-title {
    font-size: var(--font-size-3xl);
  }
  
  .section-subtitle {
    font-size: var(--font-size-lg);
  }
}
