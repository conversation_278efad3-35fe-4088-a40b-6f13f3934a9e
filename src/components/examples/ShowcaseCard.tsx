import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>er, CardTitle, CardDescription, CardContent, CardFooter } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui';
import { FadeIn, ScaleIn, Hover } from '../animations';

interface ShowcaseCardProps {
  title: string;
  description: string;
  tags?: string[];
  delay?: number;
}

export const ShowcaseCard: React.FC<ShowcaseCardProps> = ({
  title,
  description,
  tags = [],
  delay = 0
}) => {
  return (
    <TooltipProvider>
      <FadeIn delay={delay} direction="up">
        <Hover scale={1.02} y={-8}>
          <Card className="h-full">
            <CardHeader>
              <ScaleIn delay={delay + 0.2}>
                <CardTitle className="text-xl">{title}</CardTitle>
              </ScaleIn>
              <CardDescription>{description}</CardDescription>
            </CardHeader>
            
            <CardContent>
              <div className="flex flex-wrap gap-2 mb-4">
                {tags.map((tag, index) => (
                  <FadeIn key={tag} delay={delay + 0.3 + index * 0.1} direction="left">
                    <Badge variant="secondary">{tag}</Badge>
                  </FadeIn>
                ))}
              </div>
            </CardContent>
            
            <CardFooter className="flex justify-between">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="sm">
                    Learn More
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Click to learn more about {title}</p>
                </TooltipContent>
              </Tooltip>
              
              <Button size="sm">
                Get Started
              </Button>
            </CardFooter>
          </Card>
        </Hover>
      </FadeIn>
    </TooltipProvider>
  );
};