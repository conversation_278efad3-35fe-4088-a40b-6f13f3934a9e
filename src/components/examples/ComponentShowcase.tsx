import React from 'react';
import { ShowcaseCard } from './ShowcaseCard';
import { Stagger } from '../animations';

export const ComponentShowcase: React.FC = () => {
  const examples = [
    {
      title: "Modern UI Components",
      description: "Built with shadcn/ui for consistency and accessibility",
      tags: ["shadcn/ui", "Accessible", "TypeScript"]
    },
    {
      title: "Smooth Animations",
      description: "Framer Motion powered animations with stagger effects",
      tags: ["Framer Motion", "Stagger", "Smooth"]
    },
    {
      title: "Organized Structure",
      description: "Well-organized component categories for better maintainability",
      tags: ["Structure", "Maintainable", "Scalable"]
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Component Showcase</h1>
        <p className="text-lg text-muted-foreground">
          Demonstrating the new organized component structure with animations
        </p>
      </div>
      
      <Stagger staggerDelay={0.2}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {examples.map((example, index) => (
            <ShowcaseCard
              key={example.title}
              title={example.title}
              description={example.description}
              tags={example.tags}
              delay={index * 0.1}
            />
          ))}
        </div>
      </Stagger>
    </div>
  );
};