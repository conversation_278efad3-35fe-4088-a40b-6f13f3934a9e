# Component Library Structure

This project now uses a well-organized component structure with shadcn/ui integration and enhanced animations.

## Directory Structure

```
src/components/
├── ui/                    # shadcn/ui components + legacy UI components
├── animations/            # Reusable animation components
├── layout/               # Layout components (Header, Footer, Container)
├── sections/             # Page section components (Hero, Services, etc.)
├── features/             # Feature-specific components
├── examples/             # Example/showcase components
├── common/               # Legacy common components (being migrated)
├── enhanced/             # Enhanced versions of components
└── [Section Components]/ # Individual section folders (<PERSON>, <PERSON>er, etc.)
```

## Component Categories

### 1. UI Components (`/ui`)
Modern, accessible components based on shadcn/ui:
- `Button` - Versatile button with multiple variants
- `Card` - Flexible card component with header, content, footer
- `Tooltip` - Accessible tooltip with Radix UI
- `Badge` - Status and category badges
- `Input` - Form input component

**Usage:**
```tsx
import { Button, Card, CardHeader, CardTitle, CardContent } from '@/components/ui';

<Card>
  <CardHeader>
    <CardTitle>My Card</CardTitle>
  </CardHeader>
  <CardContent>
    <Button variant="default">Click me</Button>
  </CardContent>
</Card>
```

### 2. Animation Components (`/animations`)
Reusable animation wrappers using Framer Motion:
- `FadeIn` - Fade in with directional movement
- `ScaleIn` - Scale up animation
- `SlideIn` - Slide in from any direction
- `Stagger` - Staggered animations for lists
- `Hover` - Hover effects wrapper

**Usage:**
```tsx
import { FadeIn, Stagger, Hover } from '@/components/animations';

<Stagger staggerDelay={0.1}>
  <FadeIn direction="up" delay={0.2}>
    <Hover scale={1.05}>
      <div>Animated content</div>
    </Hover>
  </FadeIn>
</Stagger>
```

### 3. Layout Components (`/layout`)
Core layout structure:
- `Header` - Site header with navigation
- `Footer` - Site footer
- `Container` - Content container with responsive padding

### 4. Section Components (`/sections`)
Page-specific sections:
- `Hero` - Hero/banner sections
- `Services` - Services showcase
- `ProjectsSection` - Projects display
- `ParallaxSection` - Parallax effects

### 5. Feature Components (`/features`)
Specific functionality components:
- `AnimatedTitle` - Animated text titles
- `ProjectCard` - Project display cards
- `SocialIcons` - Social media icons
- `ThemeSwitch` - Dark/light mode toggle
- `BackToTop` - Scroll to top button

## Animation System

The animation system is built on Framer Motion and provides:

### Basic Animations
```tsx
// Fade in from bottom
<FadeIn direction="up" delay={0.2} duration={0.6}>
  <div>Content</div>
</FadeIn>

// Scale in
<ScaleIn delay={0.1} scale={0.8}>
  <div>Content</div>
</ScaleIn>

// Slide in from right
<SlideIn direction="right" distance={50}>
  <div>Content</div>
</SlideIn>
```

### Staggered Animations
```tsx
<Stagger staggerDelay={0.1} direction="up">
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</Stagger>
```

### Hover Effects
```tsx
<Hover scale={1.05} y={-5} rotate={2}>
  <Card>Hover me!</Card>
</Hover>
```

## Theme Integration

The project uses both custom CSS variables and shadcn/ui theme system:

### Custom Theme Variables
- Available in both light and dark modes
- Consistent color palette
- Typography scale
- Spacing system

### shadcn/ui Theme
- HSL-based color system
- Automatic dark mode support
- Accessible color contrasts

## Best Practices

### 1. Component Organization
- Use the appropriate category folder
- Keep components focused and single-purpose
- Export from category index files

### 2. Animation Usage
- Use consistent timing (0.6s for main animations)
- Stagger related elements with 0.1s delays
- Respect user's motion preferences

### 3. Styling
- Prefer shadcn/ui components for new features
- Use Tailwind classes with the `cn()` utility
- Maintain theme consistency

### 4. Accessibility
- All components include proper ARIA attributes
- Keyboard navigation support
- Screen reader compatibility

## Migration Guide

### From Legacy Components
```tsx
// Old
import { Button } from '@/components/ui/Button';

// New
import { Button } from '@/components/ui';
// or for legacy compatibility
import { LegacyButton as Button } from '@/components/ui';
```

### Adding Animations
```tsx
// Before
<div className="my-component">Content</div>

// After
<FadeIn direction="up" delay={0.2}>
  <div className="my-component">Content</div>
</FadeIn>
```

## Examples

See `/examples/ShowcaseCard.tsx` for a comprehensive example combining:
- shadcn/ui components
- Animation system
- Proper TypeScript types
- Accessibility features