.projects-section {
  position: relative;
  padding: 6rem 0;
  background-color: var(--color-slate-50);
  overflow: hidden;
}

.projects-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E%3Cg fill='%23a855f7' fill-opacity='0.05'%3E%3Cpath d='M0 0h40v40H0V0zm40 40h40v40H40V40zm0-40h2l-2 2V0zm0 4l4-4h2l-6 6V4zm0 4l8-8h2L40 10V8zm0 4L52 0h2L40 14v-2zm0 4L56 0h2L40 18v-2zm0 4L60 0h2L40 22v-2zm0 4L64 0h2L40 26v-2zm0 4L68 0h2L40 30v-2zm0 4L72 0h2L40 34v-2zm0 4L76 0h2L40 38v-2zm0 4L80 0v2L42 40h-2zm4 0L80 4v2L46 40h-2zm4 0L80 8v2L50 40h-2zm4 0l28-28v2L54 40h-2zm4 0l24-24v2L58 40h-2zm4 0l20-20v2L62 40h-2zm4 0l16-16v2L66 40h-2zm4 0l12-12v2L70 40h-2zm4 0l8-8v2l-6 6h-2zm4 0l4-4v2l-2 2h-2z'/%3E%3C/g%3E%3C/svg%3E"),
    linear-gradient(135deg, rgba(168, 85, 247, 0.08) 0%, rgba(99, 102, 241, 0.05) 100%);
  background-size: 80px 80px, cover;
  z-index: 0;
  opacity: 0.8;
}

/* 项目过滤器 */
.projects-filter {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
  position: relative;
  z-index: 10; /* 确保按钮在最上层 */
}

.filter-btn {
  padding: 0.625rem 1.25rem;
  background-color: transparent;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius-full);
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal) var(--ease-custom);
  position: relative;
  overflow: hidden;
}

.filter-btn:hover {
  border-color: var(--color-indigo-400);
  color: var(--color-indigo-600);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.15);
}

.filter-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(99, 102, 241, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  z-index: -1;
}

.filter-btn:hover::after {
  width: 150%;
  height: 150%;
}

.filter-btn:active {
  transform: translateY(0);
}

.filter-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.filter-btn.active {
  background: linear-gradient(135deg, var(--color-indigo-500), var(--color-purple-500));
  border-color: transparent;
  color: var(--color-white);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.25);
}

/* 项目内容区域 */
.projects-content {
  position: relative;
  width: 100%;
  margin-bottom: 2rem;
  min-height: 400px;
  z-index: 1; /* 确保内容在按钮下方 */
}

/* 项目网格 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2.5rem;
  margin-bottom: 3rem;
}

/* 轮播动画效果 */
.slide-right-enter {
  opacity: 0;
  transform: translateX(100%);
}

.slide-right-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 500ms ease;
}

.slide-right-exit {
  opacity: 1;
  transform: translateX(0);
}

.slide-right-exit-active {
  opacity: 0;
  transform: translateX(-100%);
  transition: all 500ms ease;
}

.slide-left-enter {
  opacity: 0;
  transform: translateX(-100%);
}

.slide-left-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 500ms ease;
}

.slide-left-exit {
  opacity: 1;
  transform: translateX(0);
}

.slide-left-exit-active {
  opacity: 0;
  transform: translateX(100%);
  transition: all 500ms ease;
}

/* 项目卡片 */
.project-card {
  position: relative;
  background-color: var(--color-white);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important; /* 统一过渡效果 */
  opacity: 0;
  transform: translateY(20px);
  will-change: transform, box-shadow, opacity;
  animation-fill-mode: forwards;
}

.project-card.fade-up {
  opacity: 1;
  transform: translateY(0);
}

.project-card:hover {
  transform: translateY(-12px) !important; /* 使用 !important 确保覆盖内联样式 */
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
}

/* 不规则形状卡片 */
.projects-grid .project-card:nth-child(3n+1) {
  border-radius: 30px 10px 40px 15px;
}

.projects-grid .project-card:nth-child(3n+2) {
  border-radius: 15px 35px 10px 25px;
}

.projects-grid .project-card:nth-child(3n+3) {
  border-radius: 20px 20px 40px 10px;
}

/* 项目图片 */
.project-image {
  position: relative;
  width: 100%;
  height: 220px;
  overflow: hidden;
  transform: translateZ(0); /* 启用GPU加速 */
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 1.2s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: transform;
  transform-origin: center center;
}

.project-card:hover .project-image img {
  transform: scale(1.15);
  transition: transform 1.2s cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* 项目覆盖层 */
.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(79, 70, 229, 0.5), rgba(79, 70, 229, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.19, 1, 0.22, 1);
  will-change: opacity;
  transform: translateZ(0); /* 启用GPU加速 */
  backdrop-filter: blur(0px);
  transition: opacity 0.8s cubic-bezier(0.19, 1, 0.22, 1),
              backdrop-filter 0.8s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
  backdrop-filter: blur(2px);
}

.project-link {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  color: var(--color-indigo-600);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transform: translateY(50px) scale(0.9);
  opacity: 0;
  transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1),
              opacity 0.6s cubic-bezier(0.19, 1, 0.22, 1),
              background 0.3s ease,
              box-shadow 0.3s ease,
              color 0.3s ease;
  will-change: transform, opacity, scale;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.project-card:hover .project-link {
  transform: translateY(0) scale(1);
  opacity: 1;
  transition-delay: 0.1s;
}

.project-link:hover {
  background: var(--color-white);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
  transform: translateY(-5px) scale(1.05);
  color: var(--color-indigo-700);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1),
              background 0.3s ease,
              box-shadow 0.3s ease,
              color 0.3s ease;
}

/* 项目信息 */
.project-info {
  padding: 1.5rem;
}

.project-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: 0.5rem;
}

.project-category {
  font-size: var(--font-size-xs);
  color: var(--color-indigo-500);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: var(--font-weight-medium);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.project-category::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(to right, var(--color-indigo-400), var(--color-purple-400));
  border-radius: var(--border-radius-full);
}

.project-description {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  line-height: 1.6;
}

/* 无项目提示 */
.no-projects {
  text-align: center;
  padding: 3rem 0;
  color: var(--color-gray-500);
}

/* 查看更多按钮 */
.projects-more {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
  }
  
  .project-image {
    height: 180px;
  }
  
  .projects-content {
    min-height: 350px;
  }
}

@media (max-width: 640px) {
  .projects-filter {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 1rem;
    justify-content: flex-start;
  }
  
  .filter-btn {
    white-space: nowrap;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .projects-content {
    min-height: 300px;
  }
}
