.theme-switch {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  transition: all var(--transition-normal) var(--ease-custom);
}

.switch-track {
  position: relative;
  width: 50px;
  height: 26px;
  background: linear-gradient(135deg, var(--color-indigo-200), var(--color-purple-200));
  border-radius: 30px;
  margin-right: 0.75rem;
  padding: 3px;
  transition: all var(--transition-normal) var(--ease-custom);
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.1);
}

.theme-switch.dark .switch-track {
  background: linear-gradient(135deg, var(--color-indigo-800), var(--color-purple-800));
}

.switch-thumb {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, var(--color-indigo-500), var(--color-purple-500));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  transition: all 0.3s var(--ease-custom);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.theme-switch.dark .switch-thumb {
  left: calc(100% - 23px);
  background: linear-gradient(135deg, var(--color-indigo-300), var(--color-purple-300));
}

.switch-thumb svg {
  width: 12px;
  height: 12px;
}

.sun-icon, .moon-icon {
  transition: all 0.3s var(--ease-custom);
}

.theme-switch:hover .switch-thumb {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.switch-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  transition: color 0.3s var(--ease-custom);
}

.theme-switch.dark .switch-label {
  color: var(--color-gray-300);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .switch-track {
    width: 44px;
    height: 24px;
  }
  
  .switch-thumb {
    width: 18px;
    height: 18px;
  }
  
  .theme-switch.dark .switch-thumb {
    left: calc(100% - 21px);
  }
  
  .switch-thumb svg {
    width: 10px;
    height: 10px;
  }
}

/* 动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.5);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

.theme-switch:active .switch-thumb {
  animation: pulse 0.5s;
}
