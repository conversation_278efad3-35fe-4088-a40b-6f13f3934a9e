.card {
  position: relative;
  height: 100%;
  min-height: 280px;
  transition: all var(--transition-normal) var(--ease-custom);
  perspective: 1000px;
  cursor: pointer;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 2rem;
  background-color: var(--color-white);
  border-radius: var(--border-radius-xl);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(0, 0, 0, 0.05);
  transition: all var(--transition-normal) var(--ease-custom);
  z-index: 1;
  display: flex;
  flex-direction: column;
}

/* 卡片悬停效果 */
.card:hover .card-inner {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(0, 0, 0, 0.05), 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 卡片图标 */
.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 20px;
  margin-bottom: 1.5rem;
  font-size: var(--font-size-2xl);
  transition: all var(--transition-normal) var(--ease-custom);
}

.card:hover .card-icon {
  transform: scale(1.1) rotate(5deg);
}

/* 卡片标题 */
.card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: 1rem;
  transition: all var(--transition-normal) var(--ease-custom);
}

/* 卡片内容 */
.card-content {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

/* 卡片操作区 */
.card-action {
  margin-top: auto;
}

.card-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal) var(--ease-custom);
  background: none;
  border: none;
  padding: 0;
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.card-link i {
  transition: transform var(--transition-normal) var(--ease-custom);
}

.card:hover .card-link i {
  transform: translateX(5px);
}

/* 颜色变体 */
.card-indigo .card-icon {
  background-color: var(--color-primary-100);
  color: var(--color-primary-600);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.card-indigo .card-title {
  color: var(--color-primary-700);
}

.card-indigo .card-link {
  color: var(--color-primary-600);
}

.card-indigo:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(79, 70, 229, 0.1), 0 0 30px rgba(79, 70, 229, 0.2);
}

.card-purple .card-icon {
  background-color: var(--color-secondary-100);
  color: var(--color-secondary-600);
  box-shadow: 0 4px 12px rgba(147, 51, 234, 0.15);
}

.card-purple .card-title {
  color: var(--color-secondary-700);
}

.card-purple .card-link {
  color: var(--color-secondary-600);
}

.card-purple:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(147, 51, 234, 0.1), 0 0 30px rgba(147, 51, 234, 0.2);
}

.card-blue .card-icon {
  background-color: var(--color-info-50);
  color: var(--color-info-600);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.card-blue .card-title {
  color: var(--color-info-600);
}

.card-blue .card-link {
  color: var(--color-info-500);
}

.card-blue:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(59, 130, 246, 0.1), 0 0 30px rgba(59, 130, 246, 0.2);
}

.card-green .card-icon {
  background-color: var(--color-success-50);
  color: var(--color-success-600);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);
}

.card-green .card-title {
  color: var(--color-success-600);
}

.card-green .card-link {
  color: var(--color-success-500);
}

.card-green:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(34, 197, 94, 0.1), 0 0 30px rgba(34, 197, 94, 0.2);
}

.card-amber .card-icon {
  background-color: var(--color-warning-50);
  color: var(--color-warning-600);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.card-amber .card-title {
  color: var(--color-warning-600);
}

.card-amber .card-link {
  color: var(--color-warning-500);
}

.card-amber:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(245, 158, 11, 0.1), 0 0 30px rgba(245, 158, 11, 0.2);
}

.card-orange .card-icon {
  background-color: var(--color-warning-50);
  color: var(--color-warning-600);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15);
}

.card-orange .card-title {
  color: var(--color-warning-600);
}

.card-orange .card-link {
  color: var(--color-warning-500);
}

.card-orange:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(245, 158, 11, 0.1), 0 0 30px rgba(245, 158, 11, 0.2);
}

/* 为每个卡片设计独特的不规则形状 */
/* 圆角形状 */
.shape-rounded .card-inner {
  border-radius: 2rem;
}

.shape-rounded .card-inner::before {
  content: "";
  position: absolute;
  top: -10px;
  right: -10px;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  border-radius: 50%;
  z-index: -1;
  filter: blur(1px);
}

/* 波浪形状 */
.shape-wavy .card-inner {
  border-radius: 60% 40% 30% 70% / 30% 30% 70% 70%;
}

.shape-wavy .card-inner::before {
  content: "";
  position: absolute;
  bottom: -20px;
  left: -20px;
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  border-radius: 40% 60% 70% 30% / 40% 40% 60% 60%;
  z-index: -1;
  filter: blur(1px);
}

/* 曲线形状 */
.shape-curved .card-inner {
  border-radius: 30px 70px 20px 40px;
}

.shape-curved .card-inner::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 50%;
  width: 120px;
  height: 120px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 70% 30% 50% 50% / 30% 30% 70% 70%;
  z-index: -1;
  transform: translateX(-50%);
}

/* 角形状 */
.shape-angular .card-inner {
  border-radius: 0 30px 0 30px;
}

.shape-angular .card-inner::before {
  content: "";
  position: absolute;
  bottom: -10px;
  right: 30px;
  width: 80px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0 0 30px 0;
  z-index: -1;
}

/* 有机形状 */
.shape-organic .card-inner {
  border-radius: 50px 20px 60px 30px;
}

/* .shape-organic .card-inner::before {
  content: "";
  position: absolute;
  top: 20px;
  right: -15px;
  width: 100px;
  height: 100px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  z-index: -1;
} */

/* 水滴形状 */
.shape-blob .card-inner {
  border-radius: 70% 30% 50% 50% / 30% 50% 50% 70%;
}

.shape-blob .card-inner::before {
  content: "";
  position: absolute;
  bottom: -20px;
  left: 20px;
  width: 130px;
  height: 130px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50% 50% 30% 70% / 50% 50% 70% 30%;
  z-index: -1;
}

/* 调整左上角卡片的高度 */
.parallax-cards > div:first-child {
  min-height: 320px;
}

/* 视差卡片特殊效果 */
.parallax-card {
  transition: transform 0.5s var(--ease-custom);
}

.parallax-card .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  z-index: 20;
  /* 变透明的原因 */
  /* background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%); */
  backdrop-filter: blur(10px);
}

.parallax-card:hover .card-inner {
  /* box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.25), 0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 40px rgba(0, 0, 0, 0.1); */
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.parallax-cards > div:nth-child(odd) {
  transform-origin: left center;
}

.parallax-cards > div:nth-child(even) {
  transform-origin: right center;
}

/* Dark theme adjustments */
.theme-dark .card-inner {
  background-color: var(--color-card-bg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.theme-dark .card:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1), 0 0 20px rgba(0, 0, 0, 0.3);
}

.theme-dark .card-content {
  color: var(--color-text);
}

/* Dark theme color-specific hover effects */
.theme-dark .card-indigo:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(129, 140, 248, 0.2), 0 0 30px rgba(129, 140, 248, 0.3);
}

.theme-dark .card-purple:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(192, 132, 252, 0.2), 0 0 30px rgba(192, 132, 252, 0.3);
}

.theme-dark .card-blue:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(59, 130, 246, 0.2), 0 0 30px rgba(59, 130, 246, 0.3);
}

.theme-dark .card-green:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.2), 0 0 30px rgba(34, 197, 94, 0.3);
}

.theme-dark .card-amber:hover .card-inner,
.theme-dark .card-orange:hover .card-inner {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(245, 158, 11, 0.2), 0 0 30px rgba(245, 158, 11, 0.3);
}
