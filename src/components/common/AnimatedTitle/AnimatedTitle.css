.animated-title-container {
  position: relative;
  text-align: center;
  margin-bottom: 2rem;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
  overflow: hidden;
  padding: 1rem;
}

.animated-title-container.visible {
  opacity: 1;
  transform: translateY(0);
}

.animated-title {
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-semibold);
  font-size: 2.5rem;
  margin: 0 0 0.5rem 0;
  color: transparent;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  -webkit-background-clip: text;
  background-clip: text;
  position: relative;
  z-index: 2;
  letter-spacing: 0.02em;
  text-shadow: 0 2px 10px rgba(79, 70, 229, 0.2);
}

.animated-subtitle {
  font-family: var(--font-family-base);
  font-weight: var(--font-weight-cn-light);
  font-size: 1rem;
  color: #666;
  margin: 0;
  position: relative;
  z-index: 2;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.title-glow {
  position: absolute;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(124, 58, 237, 0.2) 0%, rgba(79, 70, 229, 0.1) 40%, transparent 70%);
  top: calc(var(--y) - 75px);
  left: calc(var(--x) - 75px);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.animated-title-container:hover .title-glow {
  opacity: 1;
}

/* 暗色主题样式 */
.theme-dark .animated-title {
  background: linear-gradient(90deg, #818cf8, #a78bfa);
  -webkit-background-clip: text;
  background-clip: text;
  text-shadow: 0 2px 10px rgba(129, 140, 248, 0.3);
}

.theme-dark .animated-subtitle {
  color: #a1a1aa;
}

.theme-dark .title-glow {
  background: radial-gradient(circle, rgba(167, 139, 250, 0.25) 0%, rgba(129, 140, 248, 0.15) 40%, transparent 70%);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .animated-title {
    font-size: 2rem;
  }
  
  .animated-subtitle {
    font-size: 0.9rem;
  }
}

/* 动画效果 - 标题文字渐显 */
.animated-title-container.visible .animated-title {
  animation: fadeInTitle 0.8s ease forwards;
}

.animated-title-container.visible .animated-subtitle {
  animation: fadeInSubtitle 0.8s ease forwards 0.2s;
  opacity: 0;
  animation-fill-mode: forwards;
}

@keyframes fadeInTitle {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInSubtitle {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
