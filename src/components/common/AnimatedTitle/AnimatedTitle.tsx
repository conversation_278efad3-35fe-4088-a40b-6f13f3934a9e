import React, { useState, useCallback, CSSProperties } from 'react';
import { AnimatedTitleProps } from '../../../types/common';
import { useScrollAnimation } from '../../../hooks';
import { cn } from '../../../utils';
import './AnimatedTitle.css';

interface MousePosition {
  x: number;
  y: number;
}

const AnimatedTitle: React.FC<AnimatedTitleProps> = ({ 
  title, 
  subtitle, 
  className = '',
  alignment = 'center',
  titleSize = 'large',
  subtitleSize = 'medium',
  animation = 'fade',
  delay = 0
}) => {
  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 });
  
  // Use optimized scroll animation hook
  const { elementRef, isVisible, style } = useScrollAnimation({
    animation: animation === 'fade' ? 'fade-up' : 'fade-in',
    threshold: 0.2,
    delay,
    once: true
  })

  // Optimized mouse move handler with throttling
  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>): void => {
    const element = elementRef!.current;
    if (!element) return;
    
    const rect = element.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setMousePosition({ x, y });
  }, [elementRef]);

  const containerClasses = cn(
    'animated-title-container',
    `animated-title-container--${alignment}`,
    isVisible && 'animated-title-container--visible',
    className
  );

  const titleClasses = cn(
    'animated-title',
    `animated-title--${titleSize}`
  );

  const subtitleClasses = cn(
    'animated-subtitle',
    `animated-subtitle--${subtitleSize}`
  );

  return (
    <div 
      ref={elementRef as React.RefObject<HTMLDivElement>}
      className={containerClasses}
      style={style}
      onMouseMove={handleMouseMove}
    >
      <div 
        className="title-glow"
        style={{
          '--x': `${mousePosition.x}px`,
          '--y': `${mousePosition.y}px`
        } as CSSProperties}
      />
      <h2 className={titleClasses}>{title}</h2>
      {subtitle && <p className={subtitleClasses}>{subtitle}</p>}
    </div>
  );
};

export default AnimatedTitle;
