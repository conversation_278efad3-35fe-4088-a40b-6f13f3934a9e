/* Project Card Component */
.project-card {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  cursor: pointer;
  transition: all var(--transition-normal) var(--ease-custom);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
}

/* Card Variants */
.project-card--default {
  /* Default styling already applied above */
}

.project-card--compact {
  border-radius: var(--border-radius-lg);
}

.project-card--compact .project-card__content {
  padding: var(--spacing-3);
}

.project-card--featured {
  border-radius: var(--border-radius-2xl);
  box-shadow: var(--shadow-lg);
}

.project-card--featured .project-card__content {
  padding: var(--spacing-6);
}

/* Card States */
.project-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary);
}

.project-card:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.project-card--visible {
  opacity: 1;
  transform: translateY(0);
}

/* Irregular shapes for visual interest */
.project-card:nth-child(3n+1) {
  border-radius: 30px 10px 40px 15px;
}

.project-card:nth-child(3n+2) {
  border-radius: 15px 35px 10px 25px;
}

.project-card:nth-child(3n+3) {
  border-radius: 20px 20px 40px 10px;
}

/* Image Container */
.project-card__image {
  position: relative;
  aspect-ratio: 16 / 10;
  overflow: hidden;
  background-color: var(--color-muted);
}

.project-card__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow) var(--ease-custom);
}

.project-card:hover .project-card__img {
  transform: scale(1.1);
}

/* Overlay */
.project-card__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(var(--color-primary-rgb), 0.8),
    rgba(var(--color-secondary-rgb), 0.8)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal) var(--ease-custom);
  backdrop-filter: blur(4px);
}

.project-card:hover .project-card__overlay {
  opacity: 1;
}

/* Actions */
.project-card__actions {
  display: flex;
  gap: var(--spacing-3);
}

.project-card__button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--color-white);
  color: var(--color-primary);
  border: none;
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-normal) var(--ease-custom);
  transform: translateY(20px);
  opacity: 0;
}

.project-card:hover .project-card__button {
  transform: translateY(0);
  opacity: 1;
  transition-delay: 100ms;
}

.project-card__button:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.project-card__icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

/* Content */
.project-card__content {
  padding: var(--spacing-4);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.project-card__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.project-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-heading);
  line-height: var(--line-height-tight);
  margin: 0;
  flex: 1;
}

.project-card__category {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--color-primary);
  color: var(--color-white);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  flex-shrink: 0;
}

.project-card__description {
  color: var(--color-text);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  margin: 0;
  opacity: 0.8;
  flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .project-card {
    border-radius: var(--border-radius-lg) !important;
  }
  
  .project-card__content {
    padding: var(--spacing-3);
  }
  
  .project-card__title {
    font-size: var(--font-size-base);
  }
  
  .project-card__header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-1);
  }
  
  .project-card__category {
    align-self: flex-start;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .project-card,
  .project-card__img,
  .project-card__overlay,
  .project-card__button {
    transition: none;
  }
  
  .project-card:hover {
    transform: none;
  }
  
  .project-card:hover .project-card__img {
    transform: none;
  }
}