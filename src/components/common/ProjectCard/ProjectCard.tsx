import React from 'react';
import { Project } from '../../../types/common';
import { useScrollAnimation } from '../../../hooks';
import { cn } from '../../../utils';
import './ProjectCard.css';

export interface ProjectCardProps {
  project: Project;
  index?: number;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
  showCategory?: boolean;
  onProjectClick?: (project: Project) => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  index = 0,
  className = '',
  variant = 'default',
  showCategory = true,
  onProjectClick
}) => {
  // Optimized scroll animation with staggered delay
  const { elementRef, isVisible, style } = useScrollAnimation({
    animation: 'fade-up',
    threshold: 0.1,
    delay: 100 * (index % 6), // Limit delay to prevent too long waits
    once: true
  });

  const handleClick = () => {
    if (onProjectClick) {
      onProjectClick(project);
    } else if (project.link) {
      window.open(project.link, '_blank', 'noopener,noreferrer');
    }
  };

  const cardClasses = cn(
    'project-card',
    `project-card--${variant}`,
    isVisible && 'project-card--visible',
    className
  );

  return (
    <article 
      ref={elementRef}
      className={cardClasses}
      style={style}
      onClick={handleClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick();
        }
      }}
      aria-label={`View project: ${project.title}`}
    >
      <div className="project-card__image">
        <img 
          src={project.image} 
          alt={project.title}
          loading="lazy"
          className="project-card__img"
        />
        <div className="project-card__overlay">
          <div className="project-card__actions">
            <button 
              className="project-card__button"
              onClick={(e) => {
                e.stopPropagation();
                handleClick();
              }}
              aria-label={`View details for ${project.title}`}
            >
              <svg 
                className="project-card__icon" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" 
                />
              </svg>
              View Details
            </button>
          </div>
        </div>
      </div>
      
      <div className="project-card__content">
        <header className="project-card__header">
          <h3 className="project-card__title">{project.title}</h3>
          {showCategory && (
            <span className="project-card__category">
              {project.category}
            </span>
          )}
        </header>
        
        {project.description && (
          <p className="project-card__description">
            {project.description}
          </p>
        )}
      </div>
    </article>
  );
};

export default ProjectCard;