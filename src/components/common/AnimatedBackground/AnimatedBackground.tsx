import React from 'react';
import { motion } from 'framer-motion';
import './AnimatedBackground.css';

export type BackgroundType = 
  | 'particles' 
  | 'gradient-waves' 
  | 'floating-shapes' 
  | 'grid-pattern' 
  | 'aurora' 
  | 'code-rain'
  | 'geometric'
  | 'minimal-dots';

interface AnimatedBackgroundProps {
  type: BackgroundType;
  className?: string;
  intensity?: 'low' | 'medium' | 'high';
  color?: 'primary' | 'secondary' | 'accent' | 'neutral';
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  type,
  className = '',
  intensity = 'medium',
  color = 'primary'
}) => {
  const renderBackground = () => {
    switch (type) {
      case 'particles':
        return <ParticlesBackground intensity={intensity} color={color} />;
      case 'gradient-waves':
        return <GradientWavesBackground intensity={intensity} color={color} />;
      case 'floating-shapes':
        return <FloatingShapesBackground intensity={intensity} color={color} />;
      case 'grid-pattern':
        return <GridPatternBackground intensity={intensity} color={color} />;
      case 'aurora':
        return <AuroraBackground intensity={intensity} color={color} />;
      case 'code-rain':
        return <CodeRainBackground intensity={intensity} color={color} />;
      case 'geometric':
        return <GeometricBackground intensity={intensity} color={color} />;
      case 'minimal-dots':
        return <MinimalDotsBackground intensity={intensity} color={color} />;
      default:
        return null;
    }
  };

  return (
    <div className={`animated-background animated-background--${type} ${className}`}>
      {renderBackground()}
    </div>
  );
};

// Particles Background
const ParticlesBackground: React.FC<{ intensity: string; color: string }> = ({ intensity, color }) => {
  const particleCount = intensity === 'low' ? 20 : intensity === 'medium' ? 40 : 60;
  
  return (
    <div className={`particles-bg particles-bg--${color}`}>
      {Array.from({ length: particleCount }).map((_, i) => (
        <motion.div
          key={i}
          className="particle"
          initial={{ 
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
            opacity: 0
          }}
          animate={{
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
            opacity: [0, 1, 0]
          }}
          transition={{
            duration: Math.random() * 10 + 5,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      ))}
    </div>
  );
};

// Gradient Waves Background
const GradientWavesBackground: React.FC<{ intensity: string; color: string }> = ({ intensity, color }) => {
  return (
    <div className={`gradient-waves-bg gradient-waves-bg--${color} gradient-waves-bg--${intensity}`}>
      <motion.div
        className="wave wave-1"
        animate={{ x: [-100, 100, -100] }}
        transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
      />
      <motion.div
        className="wave wave-2"
        animate={{ x: [100, -100, 100] }}
        transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
      />
      <motion.div
        className="wave wave-3"
        animate={{ x: [-50, 150, -50] }}
        transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
      />
    </div>
  );
};

// Floating Shapes Background
const FloatingShapesBackground: React.FC<{ intensity: string; color: string }> = ({ intensity, color }) => {
  const shapeCount = intensity === 'low' ? 8 : intensity === 'medium' ? 12 : 16;
  
  return (
    <div className={`floating-shapes-bg floating-shapes-bg--${color}`}>
      {Array.from({ length: shapeCount }).map((_, i) => (
        <motion.div
          key={i}
          className={`floating-shape shape-${i % 4}`}
          initial={{ 
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
            rotate: 0
          }}
          animate={{
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
            rotate: 360
          }}
          transition={{
            duration: Math.random() * 20 + 10,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      ))}
    </div>
  );
};

// Grid Pattern Background
const GridPatternBackground: React.FC<{ intensity: string; color: string }> = ({ intensity, color }) => {
  return (
    <div className={`grid-pattern-bg grid-pattern-bg--${color} grid-pattern-bg--${intensity}`}>
      <motion.div
        className="grid-overlay"
        animate={{ opacity: [0.1, 0.3, 0.1] }}
        transition={{ duration: 4, repeat: Infinity }}
      />
    </div>
  );
};

// Aurora Background
const AuroraBackground: React.FC<{ intensity: string; color: string }> = ({ intensity, color }) => {
  return (
    <div className={`aurora-bg aurora-bg--${color} aurora-bg--${intensity}`}>
      <motion.div
        className="aurora-layer aurora-layer-1"
        animate={{ 
          background: [
            'radial-gradient(ellipse at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%)',
            'radial-gradient(ellipse at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%)',
            'radial-gradient(ellipse at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%)'
          ]
        }}
        transition={{ duration: 8, repeat: Infinity }}
      />
      <motion.div
        className="aurora-layer aurora-layer-2"
        animate={{ 
          background: [
            'radial-gradient(ellipse at 80% 50%, rgba(120, 219, 255, 0.2) 0%, transparent 50%)',
            'radial-gradient(ellipse at 20% 80%, rgba(255, 219, 120, 0.2) 0%, transparent 50%)',
            'radial-gradient(ellipse at 60% 20%, rgba(219, 120, 255, 0.2) 0%, transparent 50%)'
          ]
        }}
        transition={{ duration: 10, repeat: Infinity }}
      />
    </div>
  );
};

// Code Rain Background
const CodeRainBackground: React.FC<{ intensity: string; color: string }> = ({ intensity, color }) => {
  const columnCount = intensity === 'low' ? 15 : intensity === 'medium' ? 25 : 35;
  
  return (
    <div className={`code-rain-bg code-rain-bg--${color}`}>
      {Array.from({ length: columnCount }).map((_, i) => (
        <motion.div
          key={i}
          className="code-column"
          style={{ left: `${(i / columnCount) * 100}%` }}
          animate={{ y: [-100, window.innerHeight + 100] }}
          transition={{
            duration: Math.random() * 3 + 2,
            repeat: Infinity,
            delay: Math.random() * 2,
            ease: "linear"
          }}
        >
          {Array.from({ length: 10 }).map((_, j) => (
            <span key={j} className="code-char">
              {String.fromCharCode(33 + Math.random() * 94)}
            </span>
          ))}
        </motion.div>
      ))}
    </div>
  );
};

// Geometric Background
const GeometricBackground: React.FC<{ intensity: string; color: string }> = ({ intensity, color }) => {
  return (
    <div className={`geometric-bg geometric-bg--${color} geometric-bg--${intensity}`}>
      <motion.div
        className="geometric-pattern"
        animate={{ rotate: 360 }}
        transition={{ duration: 60, repeat: Infinity, ease: "linear" }}
      />
    </div>
  );
};

// Minimal Dots Background
const MinimalDotsBackground: React.FC<{ intensity: string; color: string }> = ({ intensity, color }) => {
  return (
    <div className={`minimal-dots-bg minimal-dots-bg--${color} minimal-dots-bg--${intensity}`}>
      <motion.div
        className="dots-pattern"
        animate={{ opacity: [0.3, 0.6, 0.3] }}
        transition={{ duration: 3, repeat: Infinity }}
      />
    </div>
  );
};

export default AnimatedBackground;