.animated-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

/* Particles Background */
.particles-bg {
  position: relative;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  pointer-events: none;
}

.particles-bg--primary .particle {
  background: var(--color-primary);
  box-shadow: 0 0 10px var(--color-primary);
}

.particles-bg--secondary .particle {
  background: var(--color-secondary);
  box-shadow: 0 0 10px var(--color-secondary);
}

.particles-bg--accent .particle {
  background: var(--color-accent);
  box-shadow: 0 0 10px var(--color-accent);
}

.particles-bg--neutral .particle {
  background: var(--color-gray-400);
  box-shadow: 0 0 10px var(--color-gray-400);
}

/* Gradient Waves Background */
.gradient-waves-bg {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.wave {
  position: absolute;
  width: 200%;
  height: 200%;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.1;
}

.wave-1 {
  top: -50%;
  left: -50%;
}

.wave-2 {
  top: -30%;
  right: -50%;
}

.wave-3 {
  bottom: -50%;
  left: -30%;
}

.gradient-waves-bg--primary .wave-1 {
  background: radial-gradient(circle, var(--color-primary) 0%, transparent 70%);
}

.gradient-waves-bg--primary .wave-2 {
  background: radial-gradient(circle, var(--color-primary-300) 0%, transparent 70%);
}

.gradient-waves-bg--primary .wave-3 {
  background: radial-gradient(circle, var(--color-primary-500) 0%, transparent 70%);
}

.gradient-waves-bg--secondary .wave-1 {
  background: radial-gradient(circle, var(--color-secondary) 0%, transparent 70%);
}

.gradient-waves-bg--secondary .wave-2 {
  background: radial-gradient(circle, var(--color-secondary-300) 0%, transparent 70%);
}

.gradient-waves-bg--secondary .wave-3 {
  background: radial-gradient(circle, var(--color-secondary-500) 0%, transparent 70%);
}

.gradient-waves-bg--high .wave {
  opacity: 0.2;
}

.gradient-waves-bg--low .wave {
  opacity: 0.05;
}

/* Floating Shapes Background */
.floating-shapes-bg {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-shape {
  position: absolute;
  opacity: 0.1;
  border-radius: 20% 80% 30% 70%;
}

.shape-0 {
  width: 60px;
  height: 60px;
}

.shape-1 {
  width: 80px;
  height: 40px;
  border-radius: 50%;
}

.shape-2 {
  width: 40px;
  height: 80px;
  border-radius: 30% 70% 70% 30%;
}

.shape-3 {
  width: 100px;
  height: 60px;
  border-radius: 60% 40% 30% 70%;
}

.floating-shapes-bg--primary .floating-shape {
  background: linear-gradient(45deg, var(--color-primary), var(--color-primary-300));
}

.floating-shapes-bg--secondary .floating-shape {
  background: linear-gradient(45deg, var(--color-secondary), var(--color-secondary-300));
}

/* Grid Pattern Background */
.grid-pattern-bg {
  position: relative;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(var(--color-primary-rgb), 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(var(--color-primary-rgb), 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

.grid-pattern-bg--medium {
  background-size: 40px 40px;
}

.grid-pattern-bg--high {
  background-size: 30px 30px;
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 50%, rgba(var(--color-primary-rgb), 0.1) 0%, transparent 50%);
}

/* Aurora Background */
.aurora-bg {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, 
    rgba(var(--color-primary-rgb), 0.05) 0%, 
    rgba(var(--color-secondary-rgb), 0.05) 50%, 
    rgba(var(--color-primary-rgb), 0.05) 100%);
}

.aurora-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  filter: blur(60px);
}

.aurora-bg--high .aurora-layer {
  filter: blur(40px);
}

.aurora-bg--low .aurora-layer {
  filter: blur(80px);
}

/* Code Rain Background */
.code-rain-bg {
  position: relative;
  width: 100%;
  height: 100%;
  font-family: var(--font-family-mono);
  font-size: 14px;
  overflow: hidden;
}

.code-column {
  position: absolute;
  top: 0;
  display: flex;
  flex-direction: column;
  opacity: 0.3;
}

.code-char {
  color: var(--color-primary);
  text-shadow: 0 0 5px var(--color-primary);
  line-height: 1.2;
  animation: flicker 2s infinite alternate;
}

@keyframes flicker {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

.code-rain-bg--secondary .code-char {
  color: var(--color-secondary);
  text-shadow: 0 0 5px var(--color-secondary);
}

/* Geometric Background */
.geometric-bg {
  position: relative;
  width: 100%;
  height: 100%;
}

.geometric-pattern {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200%;
  height: 200%;
  transform: translate(-50%, -50%);
  background-image: 
    conic-gradient(from 0deg at 50% 50%, 
      transparent 0deg, 
      rgba(var(--color-primary-rgb), 0.05) 60deg, 
      transparent 120deg, 
      rgba(var(--color-primary-rgb), 0.05) 180deg, 
      transparent 240deg, 
      rgba(var(--color-primary-rgb), 0.05) 300deg, 
      transparent 360deg);
  border-radius: 50%;
}

.geometric-bg--high .geometric-pattern {
  background-image: 
    conic-gradient(from 0deg at 50% 50%, 
      transparent 0deg, 
      rgba(var(--color-primary-rgb), 0.1) 60deg, 
      transparent 120deg, 
      rgba(var(--color-primary-rgb), 0.1) 180deg, 
      transparent 240deg, 
      rgba(var(--color-primary-rgb), 0.1) 300deg, 
      transparent 360deg);
}

/* Minimal Dots Background */
.minimal-dots-bg {
  position: relative;
  width: 100%;
  height: 100%;
}

.dots-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 25% 25%, rgba(var(--color-primary-rgb), 0.1) 2px, transparent 2px),
                    radial-gradient(circle at 75% 25%, rgba(var(--color-primary-rgb), 0.1) 2px, transparent 2px),
                    radial-gradient(circle at 25% 75%, rgba(var(--color-primary-rgb), 0.1) 2px, transparent 2px),
                    radial-gradient(circle at 75% 75%, rgba(var(--color-primary-rgb), 0.1) 2px, transparent 2px);
  background-size: 100px 100px;
}

.minimal-dots-bg--medium .dots-pattern {
  background-size: 80px 80px;
}

.minimal-dots-bg--high .dots-pattern {
  background-size: 60px 60px;
}

/* Dark theme adjustments */
.theme-dark .animated-background {
  opacity: 0.7;
}

.theme-dark .particles-bg--neutral .particle {
  background: var(--color-gray-600);
  box-shadow: 0 0 10px var(--color-gray-600);
}

.theme-dark .grid-pattern-bg {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
}

.theme-dark .code-char {
  color: var(--color-primary-300);
  text-shadow: 0 0 5px var(--color-primary-300);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .animated-background {
    opacity: 0.5;
  }
  
  .particles-bg .particle {
    width: 3px;
    height: 3px;
  }
  
  .code-rain-bg {
    font-size: 12px;
  }
  
  .floating-shape {
    transform: scale(0.7);
  }
}