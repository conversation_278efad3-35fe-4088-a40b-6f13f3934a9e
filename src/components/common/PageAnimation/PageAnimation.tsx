import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './PageAnimation.css';

export type AnimationType = 
  | 'fade-in' 
  | 'slide-up' 
  | 'slide-down' 
  | 'slide-left' 
  | 'slide-right'
  | 'scale-up' 
  | 'rotate-in' 
  | 'flip-in'
  | 'bounce-in'
  | 'elastic-in';

interface PageAnimationProps {
  children: React.ReactNode;
  type?: AnimationType;
  duration?: number;
  delay?: number;
  className?: string;
  stagger?: boolean;
  staggerDelay?: number;
}

const PageAnimation: React.FC<PageAnimationProps> = ({
  children,
  type = 'fade-in',
  duration = 0.6,
  delay = 0,
  className = '',
  stagger = false,
  staggerDelay = 0.1
}) => {
  const getAnimationVariants = () => {
    switch (type) {
      case 'fade-in':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 }
        };
      
      case 'slide-up':
        return {
          initial: { opacity: 0, y: 50 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: -50 }
        };
      
      case 'slide-down':
        return {
          initial: { opacity: 0, y: -50 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: 50 }
        };
      
      case 'slide-left':
        return {
          initial: { opacity: 0, x: 50 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: -50 }
        };
      
      case 'slide-right':
        return {
          initial: { opacity: 0, x: -50 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: 50 }
        };
      
      case 'scale-up':
        return {
          initial: { opacity: 0, scale: 0.8 },
          animate: { opacity: 1, scale: 1 },
          exit: { opacity: 0, scale: 0.8 }
        };
      
      case 'rotate-in':
        return {
          initial: { opacity: 0, rotate: -10, scale: 0.9 },
          animate: { opacity: 1, rotate: 0, scale: 1 },
          exit: { opacity: 0, rotate: 10, scale: 0.9 }
        };
      
      case 'flip-in':
        return {
          initial: { opacity: 0, rotateY: -90 },
          animate: { opacity: 1, rotateY: 0 },
          exit: { opacity: 0, rotateY: 90 }
        };
      
      case 'bounce-in':
        return {
          initial: { opacity: 0, scale: 0.3 },
          animate: { 
            opacity: 1, 
            scale: 1,
            transition: {
              type: "spring",
              stiffness: 400,
              damping: 10
            }
          },
          exit: { opacity: 0, scale: 0.3 }
        };
      
      case 'elastic-in':
        return {
          initial: { opacity: 0, scale: 0 },
          animate: { 
            opacity: 1, 
            scale: 1,
            transition: {
              type: "spring",
              stiffness: 300,
              damping: 20
            }
          },
          exit: { opacity: 0, scale: 0 }
        };
      
      default:
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 }
        };
    }
  };

  const variants = getAnimationVariants();

  if (stagger && React.Children.count(children) > 1) {
    return (
      <motion.div
        className={`page-animation page-animation--${type} ${className}`}
        initial="initial"
        animate="animate"
        exit="exit"
        transition={{ duration, delay }}
      >
        {React.Children.map(children, (child, index) => (
          <motion.div
            key={index}
            variants={variants}
            transition={{ 
              duration, 
              delay: delay + (index * staggerDelay),
              type: type.includes('bounce') || type.includes('elastic') ? 'spring' : 'tween'
            }}
          >
            {child}
          </motion.div>
        ))}
      </motion.div>
    );
  }

  return (
    <motion.div
      className={`page-animation page-animation--${type} ${className}`}
      variants={variants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{ 
        duration, 
        delay,
        type: type.includes('bounce') || type.includes('elastic') ? 'spring' : 'tween'
      }}
    >
      {children}
    </motion.div>
  );
};

// Container component for page transitions
interface PageTransitionProps {
  children: React.ReactNode;
  type?: AnimationType;
  duration?: number;
  className?: string;
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  type = 'fade-in',
  duration = 0.6,
  className = ''
}) => {
  return (
    <AnimatePresence mode="wait">
      <PageAnimation 
        type={type} 
        duration={duration} 
        className={className}
      >
        {children}
      </PageAnimation>
    </AnimatePresence>
  );
};

// Staggered animation for lists
interface StaggeredListProps {
  children: React.ReactNode;
  type?: AnimationType;
  staggerDelay?: number;
  className?: string;
}

export const StaggeredList: React.FC<StaggeredListProps> = ({
  children,
  type = 'slide-up',
  staggerDelay = 0.1,
  className = ''
}) => {
  return (
    <PageAnimation
      type={type}
      stagger={true}
      staggerDelay={staggerDelay}
      className={className}
    >
      {children}
    </PageAnimation>
  );
};

export default PageAnimation;