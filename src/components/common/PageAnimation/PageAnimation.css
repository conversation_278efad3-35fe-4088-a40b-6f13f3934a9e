.page-animation {
  width: 100%;
  height: 100%;
}

.page-animation--fade-in {
  transition: opacity var(--transition-normal) var(--ease-custom);
}

.page-animation--slide-up,
.page-animation--slide-down,
.page-animation--slide-left,
.page-animation--slide-right {
  transition: all var(--transition-normal) var(--ease-custom);
}

.page-animation--scale-up {
  transition: all var(--transition-normal) var(--ease-custom);
  transform-origin: center;
}

.page-animation--rotate-in {
  transition: all var(--transition-normal) var(--ease-custom);
  transform-origin: center;
}

.page-animation--flip-in {
  transition: all var(--transition-normal) var(--ease-custom);
  transform-style: preserve-3d;
  perspective: 1000px;
}

.page-animation--bounce-in,
.page-animation--elastic-in {
  transform-origin: center;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .page-animation {
    transition: opacity var(--transition-fast) ease;
  }
  
  .page-animation * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}