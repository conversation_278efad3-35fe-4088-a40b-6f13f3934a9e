.tooltip-wrapper {
  position: relative;
  display: inline-block;
}

.tooltip {
  position: absolute;
  z-index: 1000;
  pointer-events: none;
  font-size: var(--font-size-xs);
  line-height: 1.4;
}

.tooltip__content {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
  text-align: center;
  word-wrap: break-word;
  hyphens: auto;
}

.tooltip__arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 4px solid transparent;
}

/* Position-specific styles */
.tooltip--top {
  bottom: calc(100% + 8px);
  left: -50%;
  transform: translateX(-50%);
}

.tooltip--top .tooltip__arrow {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: rgba(0, 0, 0, 0.9);
}

.tooltip--bottom {
  top: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%);
}

.tooltip--bottom .tooltip__arrow {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: rgba(0, 0, 0, 0.9);
}

.tooltip--left {
  right: calc(100% + 8px);
  top: 50%;
  transform: translateY(-50%);
}

.tooltip--left .tooltip__arrow {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: rgba(0, 0, 0, 0.9);
}

.tooltip--right {
  left: calc(100% + 8px);
  top: 50%;
  transform: translateY(-50%);
}

.tooltip--right .tooltip__arrow {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: rgba(0, 0, 0, 0.9);
}

/* Dark theme support */
.theme-dark .tooltip__content {
  background: rgba(255, 255, 255, 0.95);
  color: var(--color-gray-900);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.theme-dark .tooltip--top .tooltip__arrow {
  border-top-color: rgba(255, 255, 255, 0.95);
}

.theme-dark .tooltip--bottom .tooltip__arrow {
  border-bottom-color: rgba(255, 255, 255, 0.95);
}

.theme-dark .tooltip--left .tooltip__arrow {
  border-left-color: rgba(255, 255, 255, 0.95);
}

.theme-dark .tooltip--right .tooltip__arrow {
  border-right-color: rgba(255, 255, 255, 0.95);
}

/* Responsive behavior */
@media (max-width: 768px) {
  .tooltip {
    display: none; /* Hide tooltips on mobile to prevent touch issues */
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .tooltip {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tooltip__content {
    background: black;
    color: white;
    border: 1px solid white;
  }
  
  .theme-dark .tooltip__content {
    background: white;
    color: black;
    border: 1px solid black;
  }
}