import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './Tooltip.css';

export type TooltipPosition = 'top' | 'bottom' | 'left' | 'right' | 'auto';

interface TooltipProps extends React.PropsWithChildren {
  content: string;
  position?: TooltipPosition;
  delay?: number;
  disabled?: boolean;
  className?: string;
  maxWidth?: number;
}

const Tooltip: React.FC<TooltipProps> = ({
  content,
  position = 'top',
  delay = 500,
  children,
  disabled = false,
  className = '',
  maxWidth = 250
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [actualPosition, setActualPosition] = useState<TooltipPosition>(position);
  const timeoutRef = useRef<NodeJS.Timeout | number>(0);
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const showTooltip = () => {
    if (disabled || !content) return;
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      calculatePosition();
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  const calculatePosition = () => {
    if (position !== 'auto' || !triggerRef.current || !tooltipRef.current) {
      setActualPosition(position);
      return;
    }

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    // Check if tooltip fits above
    if (triggerRect.top - tooltipRect.height - 8 >= 0) {
      setActualPosition('top');
    }
    // Check if tooltip fits below
    else if (triggerRect.bottom + tooltipRect.height + 8 <= viewport.height) {
      setActualPosition('bottom');
    }
    // Check if tooltip fits to the right
    else if (triggerRect.right + tooltipRect.width + 8 <= viewport.width) {
      setActualPosition('right');
    }
    // Check if tooltip fits to the left
    else if (triggerRect.left - tooltipRect.width - 8 >= 0) {
      setActualPosition('left');
    }
    // Default to top if nothing else fits
    else {
      setActualPosition('top');
    }
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const getTooltipVariants = () => {
    const baseVariants = {
      hidden: { opacity: 0, scale: 0.8 },
      visible: { opacity: 1, scale: 1 }
    };

    switch (actualPosition) {
      case 'top':
        return {
          hidden: { ...baseVariants.hidden, y: 10 },
          visible: { ...baseVariants.visible, y: 0 }
        };
      case 'bottom':
        return {
          hidden: { ...baseVariants.hidden, y: -10 },
          visible: { ...baseVariants.visible, y: 0 }
        };
      case 'left':
        return {
          hidden: { ...baseVariants.hidden, x: 10 },
          visible: { ...baseVariants.visible, x: 0 }
        };
      case 'right':
        return {
          hidden: { ...baseVariants.hidden, x: -10 },
          visible: { ...baseVariants.visible, x: 0 }
        };
      default:
        return baseVariants;
    }
  };

  // Clone the child element and add event handlers
  const childWithHandlers = React.cloneElement(children as any, {
    onMouseEnter: (e: React.MouseEvent) => {
      showTooltip();
      // @ts-ignore
      children.props.onMouseEnter?.(e);
    },
    onMouseLeave: (e: React.MouseEvent) => {
      hideTooltip();
      // @ts-ignore
      children.props.onMouseLeave?.(e);
    },
    onFocus: (e: React.FocusEvent) => {
      showTooltip();
      // @ts-ignore
      children.props.onFocus?.(e);
    },
    onBlur: (e: React.FocusEvent) => {
      hideTooltip();
      // @ts-ignore
      children.props.onBlur?.(e);
    }
  });

  return (
    <div className={`tooltip-wrapper ${className}`} ref={triggerRef}>
      {childWithHandlers}
      
      <AnimatePresence>
        {isVisible && (
          <motion.div
            ref={tooltipRef}
            className={`tooltip tooltip--${actualPosition}`}
            style={{ maxWidth }}
            variants={getTooltipVariants()}
            initial="hidden"
            animate="visible"
            exit="hidden"
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 20,
              duration: 0.2
            }}
          >
            <div className="tooltip__content">
              {content}
            </div>
            <div className={`tooltip__arrow tooltip__arrow--${actualPosition}`} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Tooltip;