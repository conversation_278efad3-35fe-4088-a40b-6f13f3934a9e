.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-indigo-500), var(--color-purple-500));
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius-full);
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s var(--ease-custom);
  z-index: 100;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.5);
}

.back-to-top svg {
  transition: transform 0.3s var(--ease-custom);
}

.back-to-top:hover svg {
  transform: translateY(-3px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .back-to-top {
    bottom: 1.5rem;
    right: 1.5rem;
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 640px) {
  .back-to-top {
    bottom: 1rem;
    right: 1rem;
    width: 40px;
    height: 40px;
  }
  
  .back-to-top svg {
    width: 20px;
    height: 20px;
  }
}
