import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  FiSearch,
  FiFilter,
  FiCalendar,
  FiUser,
  FiTag,
  FiClock,
  FiEye,
  FiEdit,
  FiChevronLeft,
  FiChevronRight,
  FiGrid,
  FiList,
  FiX
} from 'react-icons/fi';
import { BlogListItem, BlogFilter } from '../../../types/common';
import { useBlogList } from '../../../hooks/useBlogList';
import './BlogList.css';

export interface BlogListProps {
  className?: string;
  showSearch?: boolean;
  showFilters?: boolean;
  showPagination?: boolean;
  pageSize?: number;
  viewMode?: 'grid' | 'list';
  onBlogSelect?: (blog: BlogListItem) => void;
}

const BlogList: React.FC<BlogListProps> = ({
  className = '',
  showSearch = true,
  showFilters = true,
  showPagination = true,
  pageSize = 9,
  viewMode: initialViewMode = 'grid',
  onBlogSelect
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(initialViewMode);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<'draft' | 'published' | 'archived' | ''>('');

  const {
    blogs,
    pagination,
    isLoading,
    error,
    filter,
    updateFilter,
    clearFilter,
    currentPage,
    setCurrentPage,
    nextPage,
    previousPage,
    getUniqueCategories,
    getUniqueTags,
    searchBlogs,
    filterByTag,
    filterByCategory,
    filterByStatus
  } = useBlogList({
    pageSize,
    initialFilter: {
      search: searchQuery,
      category: selectedCategory || undefined,
      tags: selectedTags.length > 0 ? selectedTags : undefined,
      status: selectedStatus || undefined
    }
  });

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    searchBlogs(query);
  }, [searchBlogs]);

  const handleCategoryFilter = useCallback((category: string) => {
    setSelectedCategory(category);
    filterByCategory(category);
  }, [filterByCategory]);

  const handleTagFilter = useCallback((tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];
    setSelectedTags(newTags);
    updateFilter({ tags: newTags.length > 0 ? newTags : undefined });
  }, [selectedTags, updateFilter]);

  const handleStatusFilter = useCallback((status: 'draft' | 'published' | 'archived' | '') => {
    setSelectedStatus(status);
    filterByStatus(status as any);
  }, [filterByStatus]);

  const clearAllFilters = useCallback(() => {
    setSearchQuery('');
    setSelectedCategory('');
    setSelectedTags([]);
    setSelectedStatus('');
    clearFilter();
  }, [clearFilter]);

  const handleBlogClick = useCallback((blog: BlogListItem) => {
    if (onBlogSelect) {
      onBlogSelect(blog);
    }
  }, [onBlogSelect]);

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'var(--color-success-500)';
      case 'draft': return 'var(--color-warning-500)';
      case 'archived': return 'var(--color-gray-500)';
      default: return 'var(--color-gray-500)';
    }
  };

  if (isLoading) {
    return (
      <div className={`blog-list ${className}`}>
        <div className="blog-list__loading">
          <motion.div
            className="loading-spinner"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
          <span>Loading blogs...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`blog-list ${className}`}>
        <div className="blog-list__error">
          <span>{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`blog-list ${className}`}>
      {/* Header with search and controls */}
      <motion.div
        className="blog-list__header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="blog-list__title">
          <h2>Blog Posts</h2>
          <span className="blog-list__count">
            {pagination.total} {pagination.total === 1 ? 'post' : 'posts'}
          </span>
        </div>

        <div className="blog-list__controls">
          {/* Search */}
          {showSearch && (
            <div className="blog-list__search">
              <FiSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search blogs..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="search-input"
              />
              {searchQuery && (
                <button
                  onClick={() => handleSearch('')}
                  className="search-clear"
                >
                  <FiX />
                </button>
              )}
            </div>
          )}

          {/* View mode toggle */}
          <div className="blog-list__view-toggle">
            <button
              onClick={() => setViewMode('grid')}
              className={`view-toggle-btn ${viewMode === 'grid' ? 'active' : ''}`}
            >
              <FiGrid />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`view-toggle-btn ${viewMode === 'list' ? 'active' : ''}`}
            >
              <FiList />
            </button>
          </div>

          {/* Filter toggle */}
          {showFilters && (
            <button
              onClick={() => setShowFilterPanel(!showFilterPanel)}
              className={`filter-toggle ${showFilterPanel ? 'active' : ''}`}
            >
              <FiFilter />
              <span>Filters</span>
            </button>
          )}
        </div>
      </motion.div>

      {/* Filter Panel */}
      <AnimatePresence>
        {showFilterPanel && (
          <motion.div
            className="blog-list__filter-panel"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="filter-panel__content">
              {/* Categories */}
              <div className="filter-group">
                <label>Category</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => handleCategoryFilter(e.target.value)}
                >
                  <option value="">All Categories</option>
                  {getUniqueCategories().map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Status */}
              <div className="filter-group">
                <label>Status</label>
                <select
                  value={selectedStatus}
                  onChange={(e) => handleStatusFilter(e.target.value as any)}
                >
                  <option value="">All Status</option>
                  <option value="published">Published</option>
                  <option value="draft">Draft</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              {/* Tags */}
              <div className="filter-group">
                <label>Tags</label>
                <div className="tag-filters">
                  {getUniqueTags().map(tag => (
                    <button
                      key={tag}
                      onClick={() => handleTagFilter(tag)}
                      className={`tag-filter ${selectedTags.includes(tag) ? 'active' : ''}`}
                    >
                      <FiTag />
                      {tag}
                    </button>
                  ))}
                </div>
              </div>

              {/* Clear filters */}
              <button
                onClick={clearAllFilters}
                className="clear-filters-btn"
              >
                Clear All Filters
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Active filters display */}
      {(filter.search || filter.category || filter.tags?.length || filter.status) && (
        <motion.div
          className="blog-list__active-filters"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <span>Active filters:</span>
          {filter.search && (
            <span className="active-filter">
              Search: "{filter.search}"
              <button onClick={() => handleSearch('')}><FiX /></button>
            </span>
          )}
          {filter.category && (
            <span className="active-filter">
              Category: {filter.category}
              <button onClick={() => handleCategoryFilter('')}><FiX /></button>
            </span>
          )}
          {filter.status && (
            <span className="active-filter">
              Status: {filter.status}
              <button onClick={() => handleStatusFilter('')}><FiX /></button>
            </span>
          )}
          {filter.tags?.map(tag => (
            <span key={tag} className="active-filter">
              Tag: {tag}
              <button onClick={() => handleTagFilter(tag)}><FiX /></button>
            </span>
          ))}
        </motion.div>
      )}

      {/* Blog grid/list */}
      <motion.div
        className={`blog-list__content blog-list__content--${viewMode}`}
        layout
      >
        <AnimatePresence mode="wait">
          {blogs.length === 0 ? (
            <motion.div
              className="blog-list__empty"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <span>No blogs found</span>
            </motion.div>
          ) : (
            blogs.map((blog, index) => (
              <motion.article
                key={blog.id}
                className={`blog-card blog-card--${viewMode}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                whileHover={{ y: -5, scale: 1.02 }}
                onClick={() => handleBlogClick(blog)}
              >
                {/* Thumbnail */}
                {blog.thumbnail && (
                  <div className="blog-card__thumbnail">
                    <img src={blog.thumbnail} alt={blog.title} />
                    <div className="blog-card__overlay">
                      <FiEye className="overlay-icon" />
                    </div>
                  </div>
                )}

                <div className="blog-card__content">
                  {/* Status badge */}
                  <div className="blog-card__status">
                    <span 
                      className={`status-badge status-${blog.status}`}
                      style={{ backgroundColor: getStatusColor(blog.status) }}
                    >
                      {blog.status}
                    </span>
                  </div>

                  {/* Title */}
                  <h3 className="blog-card__title">
                    <Link to={`/blog/${blog.slug}`}>
                      {blog.title}
                    </Link>
                  </h3>

                  {/* Excerpt */}
                  <p className="blog-card__excerpt">{blog.excerpt}</p>

                  {/* Meta information */}
                  <div className="blog-card__meta">
                    <div className="meta-item">
                      <FiUser className="meta-icon" />
                      <span>{blog.author}</span>
                    </div>
                    <div className="meta-item">
                      <FiCalendar className="meta-icon" />
                      <span>{formatDate(blog.publishedAt || blog.createdAt)}</span>
                    </div>
                    <div className="meta-item">
                      <FiClock className="meta-icon" />
                      <span>{blog.readingTime} min read</span>
                    </div>
                  </div>

                  {/* Tags */}
                  {blog.tags.length > 0 && (
                    <div className="blog-card__tags">
                      {blog.tags.slice(0, 3).map(tag => (
                        <span key={tag} className="tag">
                          <FiTag />
                          {tag}
                        </span>
                      ))}
                      {blog.tags.length > 3 && (
                        <span className="tag-more">+{blog.tags.length - 3}</span>
                      )}
                    </div>
                  )}

                  {/* Actions */}
                  <div className="blog-card__actions">
                    <Link 
                      to={`/blog/${blog.slug}`}
                      className="action-btn action-btn--primary"
                    >
                      <FiEye />
                      Read
                    </Link>
                    <Link 
                      to={`/blog-editor/${blog.id}`}
                      className="action-btn action-btn--secondary"
                    >
                      <FiEdit />
                      Edit
                    </Link>
                  </div>
                </div>
              </motion.article>
            ))
          )}
        </AnimatePresence>
      </motion.div>

      {/* Pagination */}
      {showPagination && pagination.totalPages > 1 && (
        <motion.div
          className="blog-list__pagination"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <button
            onClick={previousPage}
            disabled={currentPage === 1}
            className="pagination-btn"
          >
            <FiChevronLeft />
            Previous
          </button>

          <div className="pagination-info">
            <span>
              Page {currentPage} of {pagination.totalPages}
            </span>
            <span className="pagination-total">
              ({pagination.total} total)
            </span>
          </div>

          <button
            onClick={nextPage}
            disabled={currentPage === pagination.totalPages}
            className="pagination-btn"
          >
            Next
            <FiChevronRight />
          </button>
        </motion.div>
      )}
    </div>
  );
};

export default BlogList;
