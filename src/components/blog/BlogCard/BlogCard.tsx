import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  <PERSON>Calendar,
  <PERSON><PERSON>ser,
  FiTag,
  FiClock,
  FiEye,
  FiEdit,
  FiArrowRight
} from 'react-icons/fi';
import { BlogListItem } from '../../../types/common';
import './BlogCard.css';

export interface BlogCardProps {
  blog: BlogListItem;
  variant?: 'default' | 'featured' | 'compact';
  showActions?: boolean;
  showThumbnail?: boolean;
  showExcerpt?: boolean;
  showMeta?: boolean;
  showTags?: boolean;
  className?: string;
  onClick?: (blog: BlogListItem) => void;
}

const BlogCard: React.FC<BlogCardProps> = ({
  blog,
  variant = 'default',
  showActions = true,
  showThumbnail = true,
  showExcerpt = true,
  showMeta = true,
  showTags = true,
  className = '',
  onClick
}) => {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'var(--color-success-500)';
      case 'draft': return 'var(--color-warning-500)';
      case 'archived': return 'var(--color-gray-500)';
      default: return 'var(--color-gray-500)';
    }
  };

  const handleClick = () => {
    if (onClick) {
      onClick(blog);
    }
  };

  return (
    <motion.article
      className={`blog-card blog-card--${variant} ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5, scale: 1.02 }}
      transition={{ duration: 0.3 }}
      onClick={handleClick}
    >
      {/* Thumbnail */}
      {showThumbnail && blog.thumbnail && (
        <div className="blog-card__thumbnail">
          <img src={blog.thumbnail} alt={blog.title} />
          <div className="blog-card__overlay">
            <FiEye className="overlay-icon" />
          </div>
          {/* Status badge on thumbnail */}
          <div className="blog-card__status-overlay">
            <span 
              className={`status-badge status-${blog.status}`}
              style={{ backgroundColor: getStatusColor(blog.status) }}
            >
              {blog.status}
            </span>
          </div>
        </div>
      )}

      <div className="blog-card__content">
        {/* Status badge (when no thumbnail) */}
        {(!showThumbnail || !blog.thumbnail) && (
          <div className="blog-card__status">
            <span 
              className={`status-badge status-${blog.status}`}
              style={{ backgroundColor: getStatusColor(blog.status) }}
            >
              {blog.status}
            </span>
          </div>
        )}

        {/* Category */}
        {blog.category && (
          <div className="blog-card__category">
            <span className="category-badge">{blog.category}</span>
          </div>
        )}

        {/* Title */}
        <h3 className="blog-card__title">
          <Link to={`/blog/${blog.slug}`}>
            {blog.title}
          </Link>
        </h3>

        {/* Excerpt */}
        {showExcerpt && (
          <p className="blog-card__excerpt">{blog.excerpt}</p>
        )}

        {/* Meta information */}
        {showMeta && (
          <div className="blog-card__meta">
            <div className="meta-item">
              <FiUser className="meta-icon" />
              <span>{blog.author}</span>
            </div>
            <div className="meta-item">
              <FiCalendar className="meta-icon" />
              <span>{formatDate(blog.publishedAt || blog.createdAt)}</span>
            </div>
            <div className="meta-item">
              <FiClock className="meta-icon" />
              <span>{blog.readingTime} min read</span>
            </div>
          </div>
        )}

        {/* Tags */}
        {showTags && blog.tags.length > 0 && (
          <div className="blog-card__tags">
            {blog.tags.slice(0, variant === 'compact' ? 2 : 3).map(tag => (
              <span key={tag} className="tag">
                <FiTag />
                {tag}
              </span>
            ))}
            {blog.tags.length > (variant === 'compact' ? 2 : 3) && (
              <span className="tag-more">
                +{blog.tags.length - (variant === 'compact' ? 2 : 3)}
              </span>
            )}
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="blog-card__actions">
            {variant === 'featured' ? (
              <Link 
                to={`/blog/${blog.slug}`}
                className="action-btn action-btn--featured"
              >
                Read Article
                <FiArrowRight />
              </Link>
            ) : (
              <>
                <Link 
                  to={`/blog/${blog.slug}`}
                  className="action-btn action-btn--primary"
                >
                  <FiEye />
                  Read
                </Link>
                <Link 
                  to={`/blog-editor/${blog.id}`}
                  className="action-btn action-btn--secondary"
                >
                  <FiEdit />
                  Edit
                </Link>
              </>
            )}
          </div>
        )}
      </div>
    </motion.article>
  );
};

export default BlogCard;
