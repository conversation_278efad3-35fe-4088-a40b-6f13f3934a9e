import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FiChevronLeft, FiChevronRight, FiGrid } from 'react-icons/fi';
import { BlogListItem } from '../../../types/common';
import './BlogNavigation.module.css';

export interface BlogNavigationProps {
  currentBlog: BlogListItem;
  previousBlog?: BlogListItem;
  nextBlog?: BlogListItem;
  allBlogsLink?: string;
  className?: string;
}

const BlogNavigation: React.FC<BlogNavigationProps> = ({
  currentBlog,
  previousBlog,
  nextBlog,
  allBlogsLink = '/blogs',
  className = ''
}) => {
  return (
    <motion.nav
      className={`blog-navigation ${className}`}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="blog-navigation__container">
        {/* Previous Post */}
        <div className="nav-item nav-item--previous">
          {previousBlog ? (
            <Link to={`/blog/${previousBlog.slug}`} className="nav-link">
              <div className="nav-direction">
                <FiChevronLeft className="nav-icon" />
                <span>Previous</span>
              </div>
              <div className="nav-content">
                <h3 className="nav-title">{previousBlog.title}</h3>
                <p className="nav-excerpt">{previousBlog.excerpt}</p>
                <div className="nav-meta">
                  <span>{previousBlog.author}</span>
                  <span>•</span>
                  <span>{previousBlog.readingTime} min read</span>
                </div>
              </div>
            </Link>
          ) : (
            <div className="nav-placeholder">
              <span>No previous post</span>
            </div>
          )}
        </div>

        {/* All Posts Link */}
        <div className="nav-item nav-item--center">
          <Link to={allBlogsLink} className="nav-link nav-link--center">
            <FiGrid className="nav-icon" />
            <span>All Posts</span>
          </Link>
        </div>

        {/* Next Post */}
        <div className="nav-item nav-item--next">
          {nextBlog ? (
            <Link to={`/blog/${nextBlog.slug}`} className="nav-link">
              <div className="nav-content">
                <h3 className="nav-title">{nextBlog.title}</h3>
                <p className="nav-excerpt">{nextBlog.excerpt}</p>
                <div className="nav-meta">
                  <span>{nextBlog.author}</span>
                  <span>•</span>
                  <span>{nextBlog.readingTime} min read</span>
                </div>
              </div>
              <div className="nav-direction">
                <span>Next</span>
                <FiChevronRight className="nav-icon" />
              </div>
            </Link>
          ) : (
            <div className="nav-placeholder">
              <span>No next post</span>
            </div>
          )}
        </div>
      </div>
    </motion.nav>
  );
};

export default BlogNavigation;
