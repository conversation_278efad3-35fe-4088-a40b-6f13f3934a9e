import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiShare2,
  FiTwitter,
  FiFacebook,
  FiLinkedin,
  FiLink,
  FiCheck,
  FiX
} from 'react-icons/fi';
import {
  shareOnTwitter,
  shareOnFacebook,
  shareOnLinkedIn,
  shareNatively,
  copyToClipboard
} from '../../../utils/blogUtils';
import './ShareButton.css';

export interface ShareButtonProps {
  title: string;
  url: string;
  excerpt?: string;
  hashtags?: string[];
  className?: string;
  variant?: 'button' | 'menu' | 'inline';
  showLabels?: boolean;
}

const ShareButton: React.FC<ShareButtonProps> = ({
  title,
  url,
  excerpt,
  hashtags,
  className = '',
  variant = 'button',
  showLabels = true
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  const handleNativeShare = async () => {
    const success = await shareNatively(title, excerpt || title, url);
    if (!success) {
      setIsMenuOpen(true);
    }
  };

  const handleCopyLink = async () => {
    const success = await copyToClipboard(url);
    if (success) {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  const shareOptions = [
    {
      name: 'Twitter',
      icon: FiTwitter,
      color: '#1DA1F2',
      action: () => shareOnTwitter(title, url, hashtags)
    },
    {
      name: 'Facebook',
      icon: FiFacebook,
      color: '#4267B2',
      action: () => shareOnFacebook(url)
    },
    {
      name: 'LinkedIn',
      icon: FiLinkedin,
      color: '#0077B5',
      action: () => shareOnLinkedIn(title, url, excerpt)
    },
    {
      name: 'Copy Link',
      icon: copySuccess ? FiCheck : FiLink,
      color: copySuccess ? '#10B981' : '#6B7280',
      action: handleCopyLink
    }
  ];

  if (variant === 'inline') {
    return (
      <div className={`share-inline ${className}`}>
        <span className="share-label">Share:</span>
        <div className="share-options">
          {shareOptions.map((option) => (
            <button
              key={option.name}
              onClick={option.action}
              className="share-option"
              style={{ color: option.color }}
              title={`Share on ${option.name}`}
            >
              <option.icon />
              {showLabels && <span>{option.name}</span>}
            </button>
          ))}
        </div>
      </div>
    );
  }

  if (variant === 'menu') {
    return (
      <div className={`share-menu-container ${className}`}>
        <div className="share-menu">
          <div className="share-menu-header">
            <h3>Share this post</h3>
            <button
              onClick={() => setIsMenuOpen(false)}
              className="share-menu-close"
            >
              <FiX />
            </button>
          </div>
          <div className="share-menu-options">
            {shareOptions.map((option) => (
              <button
                key={option.name}
                onClick={option.action}
                className="share-menu-option"
              >
                <div 
                  className="share-option-icon"
                  style={{ backgroundColor: option.color }}
                >
                  <option.icon />
                </div>
                <span>{option.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`share-button-container ${className}`}>
      <motion.button
        onClick={handleNativeShare}
        className="share-button"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <FiShare2 />
        {showLabels && <span>Share</span>}
      </motion.button>

      <AnimatePresence>
        {isMenuOpen && (
          <>
            <motion.div
              className="share-overlay"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsMenuOpen(false)}
            />
            <motion.div
              className="share-dropdown"
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <div className="share-dropdown-header">
                <h4>Share this post</h4>
                <button
                  onClick={() => setIsMenuOpen(false)}
                  className="share-dropdown-close"
                >
                  <FiX />
                </button>
              </div>
              <div className="share-dropdown-options">
                {shareOptions.map((option) => (
                  <button
                    key={option.name}
                    onClick={() => {
                      option.action();
                      if (option.name !== 'Copy Link') {
                        setIsMenuOpen(false);
                      }
                    }}
                    className="share-dropdown-option"
                  >
                    <div 
                      className="share-option-icon"
                      style={{ backgroundColor: option.color }}
                    >
                      <option.icon />
                    </div>
                    <span>{option.name}</span>
                  </button>
                ))}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ShareButton;
