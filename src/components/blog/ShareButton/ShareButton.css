/* Share Button Container */
.share-button-container {
  @apply relative;
}

.share-button {
  @apply inline-flex items-center gap-2 px-3 py-2 text-sm font-medium 
         text-gray-600 dark:text-gray-300 hover:text-gray-900 
         dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 
         rounded-lg transition-all duration-200;
}

/* Share Dropdown */
.share-overlay {
  @apply fixed inset-0 z-40;
}

.share-dropdown {
  @apply absolute top-full right-0 mt-2 w-64 bg-white dark:bg-gray-800 
         rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 
         z-50 overflow-hidden;
}

.share-dropdown-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 
         dark:border-gray-700;
}

.share-dropdown-header h4 {
  @apply text-sm font-semibold text-gray-900 dark:text-white;
}

.share-dropdown-close {
  @apply p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
         transition-colors;
}

.share-dropdown-options {
  @apply p-2;
}

.share-dropdown-option {
  @apply flex items-center gap-3 w-full p-3 text-left hover:bg-gray-50 
         dark:hover:bg-gray-700 rounded-lg transition-colors;
}

/* Share Menu (Full Screen) */
.share-menu-container {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4 
         bg-black/50 backdrop-blur-sm;
}

.share-menu {
  @apply w-full max-w-md bg-white dark:bg-gray-800 rounded-xl shadow-xl 
         overflow-hidden;
}

.share-menu-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 
         dark:border-gray-700;
}

.share-menu-header h3 {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.share-menu-close {
  @apply p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
         transition-colors;
}

.share-menu-options {
  @apply p-6 space-y-3;
}

.share-menu-option {
  @apply flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 
         dark:hover:bg-gray-700 rounded-lg transition-colors;
}

/* Share Inline */
.share-inline {
  @apply flex items-center gap-4;
}

.share-label {
  @apply text-sm font-medium text-gray-500 dark:text-gray-400;
}

.share-options {
  @apply flex items-center gap-3;
}

.share-option {
  @apply inline-flex items-center gap-2 px-3 py-2 text-sm font-medium 
         hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg 
         transition-all duration-200;
}

/* Share Option Icon */
.share-option-icon {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white;
}

.share-option-icon svg {
  @apply w-4 h-4;
}

/* Responsive Design */
@media (max-width: 640px) {
  .share-dropdown {
    @apply w-screen max-w-xs right-0;
  }
  
  .share-inline {
    @apply flex-col items-start gap-2;
  }
  
  .share-options {
    @apply flex-wrap;
  }
  
  .share-option span {
    @apply hidden;
  }
}

/* Hover Effects */
.share-button:hover {
  @apply transform scale-105;
}

.share-dropdown-option:hover .share-option-icon {
  @apply transform scale-110;
}

.share-menu-option:hover .share-option-icon {
  @apply transform scale-110;
}

/* Focus States */
.share-button:focus,
.share-dropdown-option:focus,
.share-menu-option:focus,
.share-option:focus {
  @apply ring-2 ring-blue-500 ring-offset-2 ring-offset-white 
         dark:ring-offset-gray-800;
}

/* Animation */
.share-dropdown-option,
.share-menu-option {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success State */
.share-option-icon.success {
  @apply bg-green-500;
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  .share-dropdown {
    @apply bg-gray-800 border-gray-700;
  }
  
  .share-menu {
    @apply bg-gray-800;
  }
}
