.services-section {
  position: relative;
  padding: 6rem 0;
  background-color: var(--color-slate-50);
  overflow: hidden;
}

.services-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%236366f1' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E"),
    linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.05) 100%);
  background-size: 80px 80px, cover;
  z-index: 0;
  opacity: 0.8;
}

.services-grid {
  position: relative;
  z-index: 1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2.5rem;
  margin-top: 3rem;
}

/* 服务卡片 */
.service-card {
  background-color: var(--color-white);
  padding: 2.5rem;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important; /* 统一过渡效果 */
  position: relative;
  z-index: 1;
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  will-change: transform, box-shadow, opacity;
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* 不规则形状设计 */
.service-card:nth-child(1) {
  border-radius: 40px 20px 60px 20px;
}

.service-card:nth-child(2) {
  border-radius: 20px 60px 20px 40px;
}

.service-card:nth-child(3) {
  border-radius: 60px 20px 40px 20px;
}

.service-card:nth-child(4) {
  border-radius: 20px 40px 20px 60px;
}

.service-card.fade-up {
  opacity: 1;
  transform: translateY(0);
}

.service-card::before {
  content: '';
  position: absolute;
  top: -20px;
  right: -20px;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, var(--color-indigo-300), var(--color-purple-300));
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  opacity: 0.5;
  z-index: -1;
  transition: all 1.2s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: transform;
  filter: blur(3px);
}

.service-card:hover {
  transform: translateY(-15px) rotateX(2deg) !important; /* 使用 !important 确保覆盖内联样式 */
  box-shadow: 0 25px 50px rgba(99, 102, 241, 0.2);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
}

.service-card:hover::before {
  transform: scale(1.5) rotate(20deg) translate(-10px, 10px);
  opacity: 0.7;
  filter: blur(0);
  transition: all 1.2s cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* 服务图标 */
.service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--color-indigo-500), var(--color-purple-500));
  color: var(--color-white);
  border-radius: var(--border-radius-lg);
  /* 不规则形状设计 */
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  margin-bottom: 1.5rem;
  box-shadow: 0 10px 20px rgba(99, 102, 241, 0.2);
  transition: all 1s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: transform, box-shadow;
  position: relative;
  z-index: 2;
}

.service-card:hover .service-icon {
  transform: rotate(-15deg) scale(1.2) translateY(-5px);
  box-shadow: 0 20px 40px rgba(99, 102, 241, 0.4);
  transition: all 1.2s cubic-bezier(0.25, 0.1, 0.25, 1);
  transition-delay: 0.1s;
}

.service-icon svg {
  width: 30px;
  height: 30px;
}

/* 服务标题 */
.service-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.service-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(to right, var(--color-indigo-500), var(--color-purple-500));
  border-radius: var(--border-radius-full);
  transition: width 1s cubic-bezier(0.25, 0.1, 0.25, 1), height 1s cubic-bezier(0.25, 0.1, 0.25, 1), opacity 1s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: width, height, opacity;
}

.service-card:hover .service-title::after {
  width: 100%;
  height: 4px;
  opacity: 0.8;
  transition-delay: 0.1s;
}

/* 服务描述 */
.service-description {
  font-size: var(--font-size-base);
  line-height: 1.7;
  color: var(--color-gray-700);
  margin-bottom: 1.5rem;
}

/* 服务特性列表 */
.service-features {
  margin: 1.5rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
}

.feature-icon {
  width: 16px;
  height: 16px;
  margin-right: 0.75rem;
  color: var(--color-indigo-500);
  flex-shrink: 0;
}

/* 服务CTA */
.service-cta {
  display: inline-flex;
  align-items: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-indigo-600);
  text-decoration: none;
  margin-top: 1rem;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  position: relative;
  will-change: color, transform;
  padding: 0.5rem 0;
}

.service-cta::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, var(--color-indigo-500), var(--color-purple-500));
  transition: width 1s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: width;
}

.service-cta:hover {
  color: var(--color-indigo-700);
  transform: translateX(5px);
  transition: all 1.2s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.service-cta:hover::after {
  width: 100%;
  transition-delay: 0.05s;
}

.service-cta svg {
  margin-left: 0.5rem;
  transition: transform 1s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: transform;
}

.service-cta:hover svg {
  transform: translateX(10px);
  transition: transform 1.2s cubic-bezier(0.25, 0.1, 0.25, 1);
  transition-delay: 0.2s;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .services-section {
    padding: 5rem 0;
  }
  
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .services-section {
    padding: 4rem 0;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .service-card {
    padding: 2rem;
  }
}

@media (max-width: 640px) {
  .service-icon {
    width: 60px;
    height: 60px;
  }
  
  .service-icon svg {
    width: 24px;
    height: 24px;
  }
  
  .service-title {
    font-size: var(--font-size-lg);
  }
}
