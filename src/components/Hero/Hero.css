.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 6rem 0 4rem;
  overflow: hidden;
  background: linear-gradient(135deg, var(--color-slate-50), var(--color-indigo-50));
  background-image: 
    radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.15) 0%, transparent 40%),
    radial-gradient(circle at 80% 70%, rgba(168, 85, 247, 0.1) 0%, transparent 40%),
    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.hero-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 10;
}

.hero-content {
  flex: 1;
  max-width: 600px;
  padding-right: 2rem;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.hero-title-line {
  display: block;
}

.gradient-text {
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-size: 200% auto;
  animation: gradient-shift 5s ease infinite;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.hero-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.875rem 2rem;
  border-radius: var(--border-radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal) var(--ease-custom);
  text-decoration: none;
}

.hero-button.primary {
  background: linear-gradient(135deg, var(--color-indigo-500), var(--color-purple-500));
  color: var(--color-white);
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
}

.hero-button.primary:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.5);
}

.hero-button.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: var(--color-indigo-600);
  border: 1px solid var(--color-indigo-200);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.hero-button.secondary:hover {
  transform: translateY(-5px);
  background: var(--color-white);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.hero-image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.hero-image {
  position: relative;
  width: 400px;
  height: 400px;
}

/* 装饰元素 */
.hero-decoration-1 {
  position: absolute;
  top: 20%;
  left: 10%;
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, var(--color-indigo-300), var(--color-purple-300));
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  opacity: 0.8;
  animation: float 8s ease-in-out infinite;
}

.hero-decoration-2 {
  position: absolute;
  bottom: 15%;
  right: 15%;
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, var(--color-purple-300), var(--color-indigo-300));
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  opacity: 0.6;
  animation: float 10s ease-in-out infinite reverse;
}

.hero-decoration-3 {
  position: absolute;
  top: 40%;
  right: 25%;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--color-blue-300), var(--color-indigo-300));
  border-radius: 50% 50% 50% 70% / 50% 50% 70% 60%;
  opacity: 0.7;
  animation: float 12s ease-in-out infinite;
}

/* 滚动指示器 */
.hero-scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}

.scroll-arrow {
  display: inline-block;
  font-size: var(--font-size-xl);
  color: var(--color-indigo-500);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .hero-container {
    flex-direction: column;
    text-align: center;
  }
  
  .hero-content {
    max-width: 100%;
    padding-right: 0;
    margin-bottom: 3rem;
  }
  
  .hero-buttons {
    justify-content: center;
  }
  
  .hero-image {
    width: 300px;
    height: 300px;
  }
}

@media (max-width: 640px) {
  .hero-title {
    font-size: var(--font-size-4xl);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-lg);
  }
  
  .hero-buttons {
    flex-direction: column;
    gap: 1rem;
  }
  
  .hero-image {
    width: 250px;
    height: 250px;
  }
}
