import { motion, HTMLMotionProps } from 'framer-motion';
import { ReactNode } from 'react';

interface HoverProps extends Omit<HTMLMotionProps<"div">, 'children'> {
  children: ReactNode;
  scale?: number;
  y?: number;
  rotate?: number;
  duration?: number;
}

export const Hover = ({ 
  children, 
  scale = 1.05,
  y = -5,
  rotate = 0,
  duration = 0.2,
  ...props 
}: HoverProps) => {
  return (
    <motion.div
      whileHover={{ 
        scale, 
        y, 
        rotate,
        transition: { duration }
      }}
      whileTap={{ scale: 0.95 }}
      {...props}
    >
      {children}
    </motion.div>
  );
};