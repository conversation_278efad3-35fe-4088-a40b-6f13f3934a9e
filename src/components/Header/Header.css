.site-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 0.75rem 0;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, background-color, box-shadow;
}

.site-header.scrolled {
  background-color: rgba(255, 255, 255, 0.65);
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  transform: translateZ(0);
  animation: glassEffect 0.5s forwards;
}

@keyframes glassEffect {
  0% {
    background-color: rgba(255, 255, 255, 1);
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
  }
  100% {
    background-color: rgba(255, 255, 255, 0.65);
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
  }
}

.theme-dark .site-header {
  background-color: rgba(26, 32, 44, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.theme-dark .site-header.scrolled {
  background-color: rgba(26, 32, 44, 0.65);
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transform: translateZ(0);
  animation: darkGlassEffect 0.5s forwards;
}

@keyframes darkGlassEffect {
  0% {
    background-color: rgba(26, 32, 44, 1);
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
  }
  100% {
    background-color: rgba(26, 32, 44, 0.65);
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
  }
}

.header-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  transition: all 0.5s ease;
}

.scrolled .header-container {
  height: 55px;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}

/* Logo 样式 */
.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-img {
  width: 36px;
  height: 36px;
  margin-right: 0.5rem;
  object-fit: cover;
  border-radius: 50%;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scrolled .logo-img {
  transform: scale(0.95);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
}

.logo:hover .logo-img {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.logo-text {
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  color: #333;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-family: var(--font-family-heading);
}

.theme-dark .logo-text {
  color: #f7fafc;
}

.scrolled .logo-text {
  background: linear-gradient(90deg, #3730a3, #6025c0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  letter-spacing: 0.02em;
  font-weight: var(--font-weight-semibold);
}

/* 主导航 */
.main-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  margin: 0 20px;
}

.nav-list {
  display: flex;
  gap: 2rem;
  margin: 0;
  padding: 0;
  list-style: none;
}

.nav-item {
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
}

.nav-link {
  color: #333;
  font-weight: 400;
  text-decoration: none;
  padding: 0.5rem 0;
  transition: all 0.3s ease;
  position: relative;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  font-family: var(--font-family-base);
  letter-spacing: 0.01em;
}

.scrolled .nav-link {
  color: #111;
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.015em;
  transform: translateY(1px);
}

.theme-dark .nav-link {
  color: #e2e8f0;
}

.theme-dark .scrolled .nav-link {
  color: #ffffff;
}

.nav-link:hover {
  color: #4f46e5;
}

.theme-dark .nav-link:hover {
  color: #7f9cf5;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: #4f46e5;
  transition: width 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
  opacity: 0;
  transform: translateY(3px);
}

.scrolled .nav-link::after {
  background: linear-gradient(90deg, #3730a3, #6025c0);
  height: 2px;
}

.nav-link:hover::after,
.nav-item.active .nav-link::after {
  width: 100%;
  opacity: 1;
  transform: translateY(0);
}

.theme-dark .nav-link::after {
  background: #7f9cf5;
}

.nav-link:hover::after {
  width: 100%;
}

/* 移动菜单按钮 */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1100;
}

.mobile-menu-toggle span {
  display: block;
  width: 100%;
  height: 3px;
  background-color: var(--color-primary);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

/* 移动导航 */
.mobile-nav {
  position: fixed;
  top: 0;
  right: -100%;
  width: 80%;
  max-width: 400px;
  height: 100vh;
  background-color: var(--color-card-bg);
  box-shadow: var(--shadow-xl);
  transition: right var(--transition-normal) var(--ease-custom);
  z-index: 1050;
  overflow-y: auto;
  padding: 5rem 2rem 2rem;
}

.mobile-nav.open {
  right: 0;
}

.mobile-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav-item {
  margin-bottom: 1.5rem;
}

.mobile-nav-link {
  display: block;
  font-size: var(--font-size-lg);
  color: var(--color-text);
  text-decoration: none;
  padding: 0.5rem 0;
  transition: color var(--transition-normal) var(--ease-custom);
}

.mobile-nav-link:hover {
  color: var(--color-primary);
}

.mobile-dropdown {
  margin-top: 0.5rem;
  margin-left: 1rem;
  list-style: none;
  padding: 0;
}

.mobile-dropdown-item {
  margin-top: 0.5rem;
}

.mobile-dropdown-link {
  display: block;
  color: var(--color-text);
  opacity: 0.8;
  text-decoration: none;
  padding: 0.25rem 0;
  transition: color var(--transition-normal) var(--ease-custom);
}

.mobile-dropdown-link:hover {
  color: var(--color-primary);
  opacity: 1;
}

/* 头部右侧区域 */
.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 0 0 auto;
}

/* 社交图标区 */
.social-icons {
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scrolled .social-icons {
  transform: scale(0.95);
  gap: 0.85rem;
}

/* 社交图标 */
.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 1.1rem;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scrolled .social-icon {
  color: #444;
  font-size: 1rem;
}

.theme-dark .social-icon {
  color: #a0aec0;
}

.social-icon:hover {
  color: #4f46e5;
}

.theme-dark .social-icon:hover {
  color: #7f9cf5;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-right {
    gap: 0.75rem;
  }
  
  .main-nav {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
}

@media (max-width: 640px) {
  .social-icons .social-icon:nth-child(2),
  .social-icons .social-icon:nth-child(3) {
    display: none;
  }
  
  .logo-text {
    display: none;
  }
}
