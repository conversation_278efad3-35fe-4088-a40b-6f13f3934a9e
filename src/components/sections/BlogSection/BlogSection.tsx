import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FiArrowRight, FiBookOpen, FiTrendingUp } from 'react-icons/fi';
import { BlogCard } from '../../blog';
import { useBlogList } from '../../../hooks/useBlogList';
import { initializeSampleBlogs } from '../../../data/sampleBlogs';
import './BlogSection.css';

export interface BlogSectionProps {
  className?: string;
  maxPosts?: number;
  showFeatured?: boolean;
  title?: string;
  subtitle?: string;
}

const BlogSection: React.FC<BlogSectionProps> = ({
  className = '',
  maxPosts = 6,
  showFeatured = true,
  title = "Latest Blog Posts",
  subtitle = "Insights, tutorials, and thoughts on web development"
}) => {
  // Initialize sample blogs on component mount
  React.useEffect(() => {
    initializeSampleBlogs();
  }, []);

  const {
    blogs,
    isLoading,
    error,
    getRecentBlogs
  } = useBlogList({
    pageSize: maxPosts,
    initialFilter: { status: 'published' }
  });

  // Get recent published blogs
  const recentBlogs = getRecentBlogs(maxPosts);
  const featuredBlog = recentBlogs[0];
  const otherBlogs = recentBlogs.slice(1, maxPosts);

  if (isLoading) {
    return (
      <section className={`blog-section ${className}`}>
        <div className="blog-section__container">
          <div className="blog-section__loading">
            <motion.div
              className="loading-spinner"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <span>Loading blog posts...</span>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className={`blog-section ${className}`}>
        <div className="blog-section__container">
          <div className="blog-section__error">
            <span>Failed to load blog posts</span>
          </div>
        </div>
      </section>
    );
  }

  if (recentBlogs.length === 0) {
    return (
      <section className={`blog-section ${className}`}>
        <div className="blog-section__container">
          <motion.div
            className="blog-section__header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="section-badge">
              <FiBookOpen className="badge-icon" />
              <span>Blog</span>
            </div>
            <h2 className="section-title">{title}</h2>
            <p className="section-subtitle">{subtitle}</p>
          </motion.div>
          
          <div className="blog-section__empty">
            <FiBookOpen className="empty-icon" />
            <h3>No blog posts yet</h3>
            <p>Check back soon for new content!</p>
            <Link to="/blog-editor" className="cta-button">
              <span>Write First Post</span>
              <FiArrowRight />
            </Link>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={`blog-section ${className}`}>
      <div className="blog-section__container">
        {/* Header */}
        <motion.div
          className="blog-section__header"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="section-badge">
            <FiBookOpen className="badge-icon" />
            <span>Blog</span>
          </div>
          <h2 className="section-title">{title}</h2>
          <p className="section-subtitle">{subtitle}</p>
        </motion.div>

        {/* Blog Grid */}
        <div className="blog-section__content">
          {showFeatured && featuredBlog && (
            <motion.div
              className="blog-section__featured"
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <BlogCard
                blog={featuredBlog}
                variant="featured"
                showActions={true}
                showThumbnail={true}
                showExcerpt={true}
                showMeta={true}
                showTags={true}
              />
            </motion.div>
          )}

          {otherBlogs.length > 0 && (
            <motion.div
              className="blog-section__grid"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              {otherBlogs.map((blog, index) => (
                <motion.div
                  key={blog.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 0.1 * index }}
                >
                  <BlogCard
                    blog={blog}
                    variant="default"
                    showActions={true}
                    showThumbnail={true}
                    showExcerpt={true}
                    showMeta={true}
                    showTags={true}
                  />
                </motion.div>
              ))}
            </motion.div>
          )}
        </div>

        {/* Call to Action */}
        <motion.div
          className="blog-section__cta"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <div className="cta-content">
            <div className="cta-icon">
              <FiTrendingUp />
            </div>
            <div className="cta-text">
              <h3>Explore More Articles</h3>
              <p>Discover more insights, tutorials, and stories</p>
            </div>
          </div>
          <Link to="/blogs" className="cta-button">
            <span>View All Posts</span>
            <FiArrowRight />
          </Link>
        </motion.div>

        {/* Background decoration */}
        <div className="blog-section__decoration">
          <div className="decoration-circle decoration-circle--1"></div>
          <div className="decoration-circle decoration-circle--2"></div>
          <div className="decoration-circle decoration-circle--3"></div>
        </div>
      </div>
    </section>
  );
};

export default BlogSection;
