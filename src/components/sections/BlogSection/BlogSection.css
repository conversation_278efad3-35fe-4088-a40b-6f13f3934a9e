.blog-section {
  position: relative;
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  overflow: hidden;
}

/* Animated background elements */
.blog-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(168, 85, 247, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 90%, rgba(236, 72, 153, 0.08) 0%, transparent 50%);
  animation: sectionFloat 25s ease-in-out infinite;
  pointer-events: none;
}

@keyframes sectionFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

.blog-section__container {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 10;
}

@media (min-width: 640px) {
  .blog-section__container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .blog-section__container {
    padding: 0 2rem;
  }
}

/* Header */
.blog-section__header {
  text-align: center;
  margin-bottom: 4rem;
  animation: headerSlideUp 1s ease-out;
}

@keyframes headerSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(168, 85, 247, 0.1));
  color: #6366f1;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(99, 102, 241, 0.2);
  backdrop-filter: blur(10px);
  animation: badgeFloat 3s ease-in-out infinite;
}

@keyframes badgeFloat {
  0%, 100% {
    transform: translateY(0px);
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
  }
  50% {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  }
}

.badge-icon {
  width: 1rem;
  height: 1rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleGlow 2s ease-in-out infinite alternate;
}

@media (min-width: 768px) {
  .section-title {
    font-size: 3rem;
  }
}

@keyframes titleGlow {
  from {
    filter: drop-shadow(0 0 10px rgba(102, 126, 234, 0.3));
  }
  to {
    filter: drop-shadow(0 0 20px rgba(118, 75, 162, 0.4));
  }
}

.section-subtitle {
  font-size: 1.25rem;
  color: #4b5563;
  max-width: 32rem;
  margin: 0 auto;
  line-height: 1.6;
  animation: subtitleFade 1s ease-out 0.3s both;
}

@keyframes subtitleFade {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Content */
.blog-section__content {
  @apply mb-16;
}

.blog-section__featured {
  @apply mb-12;
}

.blog-section__grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
}

/* Call to Action */
.blog-section__cta {
  @apply flex flex-col sm:flex-row items-center justify-between 
         p-8 bg-white dark:bg-gray-800 rounded-2xl shadow-lg 
         border border-gray-200 dark:border-gray-700;
}

.cta-content {
  @apply flex items-center gap-4 mb-6 sm:mb-0;
}

.cta-icon {
  @apply w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl 
         flex items-center justify-center text-blue-600 dark:text-blue-400;
}

.cta-icon svg {
  @apply w-6 h-6;
}

.cta-text h3 {
  @apply text-xl font-bold text-gray-900 dark:text-white mb-1;
}

.cta-text p {
  @apply text-gray-600 dark:text-gray-300;
}

.cta-button {
  @apply inline-flex items-center gap-2 px-6 py-3 bg-blue-600 
         hover:bg-blue-700 text-white font-medium rounded-xl 
         transition-all duration-300 hover:scale-105 hover:shadow-lg;
}

/* Loading and Error States */
.blog-section__loading,
.blog-section__error {
  @apply flex flex-col items-center justify-center py-20 text-gray-500 
         dark:text-gray-400;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full mb-4;
}

/* Empty State */
.blog-section__empty {
  @apply text-center py-20;
}

.empty-icon {
  @apply w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-6;
}

.blog-section__empty h3 {
  @apply text-2xl font-bold text-gray-900 dark:text-white mb-2;
}

.blog-section__empty p {
  @apply text-gray-600 dark:text-gray-300 mb-8;
}

/* Background Decoration */
.blog-section__decoration {
  @apply absolute inset-0 pointer-events-none;
}

.decoration-circle {
  @apply absolute rounded-full opacity-10;
}

.decoration-circle--1 {
  @apply w-96 h-96 bg-blue-500 -top-48 -right-48;
  animation: float 6s ease-in-out infinite;
}

.decoration-circle--2 {
  @apply w-64 h-64 bg-purple-500 top-1/2 -left-32;
  animation: float 8s ease-in-out infinite reverse;
}

.decoration-circle--3 {
  @apply w-32 h-32 bg-pink-500 bottom-20 right-20;
  animation: float 4s ease-in-out infinite;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-section {
    @apply py-16;
  }
  
  .blog-section__header {
    @apply mb-12;
  }
  
  .section-title {
    @apply text-3xl;
  }
  
  .section-subtitle {
    @apply text-lg;
  }
  
  .blog-section__featured {
    @apply mb-8;
  }
  
  .blog-section__grid {
    @apply gap-6;
  }
  
  .blog-section__cta {
    @apply p-6 text-center;
  }
  
  .cta-content {
    @apply flex-col text-center;
  }
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  .blog-section {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
  
  .section-title {
    background: linear-gradient(135deg, #90cdf4 0%, #a78bfa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Hover effects */
.blog-section__featured:hover {
  transform: translateY(-5px);
  transition: transform 0.3s ease;
}

.blog-section__grid > div:hover {
  transform: translateY(-3px);
  transition: transform 0.3s ease;
}

/* Focus states for accessibility */
.cta-button:focus {
  @apply ring-2 ring-blue-500 ring-offset-2 ring-offset-white 
         dark:ring-offset-gray-800;
}

/* Print styles */
@media print {
  .blog-section__decoration {
    display: none;
  }
  
  .blog-section {
    @apply bg-white text-black;
  }
}
