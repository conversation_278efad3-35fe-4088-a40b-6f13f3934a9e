/* Button Component Styles */
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  border: 1px solid transparent;
  border-radius: var(--border-radius-lg);
  font-family: var(--font-family-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal) var(--ease-custom);
  user-select: none;
  white-space: nowrap;
  outline: none;
}

/* But<PERSON> Sizes */
.btn--sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  min-height: 2rem;
}

.btn--md {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  min-height: 2.5rem;
}

.btn--lg {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-lg);
  min-height: 3rem;
}

/* <PERSON><PERSON> Variants */
.btn--primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  color: var(--color-white);
  box-shadow: 0 4px 15px rgba(var(--color-primary-rgb, 79, 70, 229), 0.4);
}

.btn--primary:hover:not(.btn--disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--color-primary-rgb, 79, 70, 229), 0.5);
}

.btn--primary:active:not(.btn--disabled) {
  transform: translateY(0);
}

.btn--secondary {
  background-color: var(--color-card-bg);
  color: var(--color-text);
  border-color: var(--color-border);
  box-shadow: var(--shadow-sm);
}

.btn--secondary:hover:not(.btn--disabled) {
  background-color: var(--color-muted);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.btn--outline {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn--outline:hover:not(.btn--disabled) {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.btn--ghost {
  background-color: transparent;
  color: var(--color-text);
  border-color: transparent;
}

.btn--ghost:hover:not(.btn--disabled) {
  background-color: var(--color-muted);
}

.btn--danger {
  background-color: var(--color-error);
  color: var(--color-white);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.btn--danger:hover:not(.btn--disabled) {
  background-color: var(--color-error-600);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.5);
}

/* Button States */
.btn--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn--loading {
  cursor: wait;
}

.btn--full-width {
  width: 100%;
}

/* Button Content */
.btn__content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.btn__icon svg {
  width: 1em;
  height: 1em;
}

/* Loading Spinner */
.btn__spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn__spinner-icon {
  width: 1em;
  height: 1em;
  animation: spin 1s linear infinite;
}

.btn__spinner-circle {
  stroke: currentColor;
  stroke-linecap: round;
  stroke-dasharray: 31.416;
  stroke-dashoffset: 31.416;
  animation: spinner-dash 2s ease-in-out infinite;
}

.btn--loading .btn__content {
  opacity: 0;
}

/* Focus States */
.btn:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes spinner-dash {
  0% {
    stroke-dasharray: 1, 31.416;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 15.708, 31.416;
    stroke-dashoffset: -7.854;
  }
  100% {
    stroke-dasharray: 15.708, 31.416;
    stroke-dashoffset: -23.562;
  }
}