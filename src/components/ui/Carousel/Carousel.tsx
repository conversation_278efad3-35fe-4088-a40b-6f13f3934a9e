import React, { ReactNode } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';
import { CommonProps } from '../../../types/common';
import { cn } from '../../../utils';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';
import './Carousel.css';

export interface CarouselProps extends CommonProps {
  slides: ReactNode[];
  autoplay?: boolean;
  autoplayDelay?: number;
  navigation?: boolean;
  pagination?: boolean;
  loop?: boolean;
  effect?: 'slide' | 'fade';
  slidesPerView?: number | 'auto';
  spaceBetween?: number;
  centeredSlides?: boolean;
  breakpoints?: Record<number, {
    slidesPerView?: number;
    spaceBetween?: number;
  }>;
}

const Carousel: React.FC<CarouselProps> = ({
  slides,
  className = '',
  autoplay = false,
  autoplayDelay = 3000,
  navigation = true,
  pagination = true,
  loop = true,
  effect = 'slide',
  slidesPerView = 1,
  spaceBetween = 30,
  centeredSlides = false,
  breakpoints,
  children
}) => {
  const modules = [
    ...(navigation ? [Navigation] : []),
    ...(pagination ? [Pagination] : []),
    ...(autoplay ? [Autoplay] : []),
    ...(effect === 'fade' ? [EffectFade] : [])
  ];

  const swiperClasses = cn(
    'carousel',
    `carousel--${effect}`,
    className
  );

  return (
    <div className={swiperClasses}>
      <Swiper
        modules={modules}
        slidesPerView={slidesPerView}
        spaceBetween={spaceBetween}
        centeredSlides={centeredSlides}
        loop={loop}
        effect={effect}
        autoplay={autoplay ? {
          delay: autoplayDelay,
          disableOnInteraction: false,
        } : false}
        navigation={navigation}
        pagination={pagination ? {
          clickable: true,
          dynamicBullets: true,
        } : false}
        breakpoints={breakpoints}
        className="carousel__swiper"
      >
        {slides.map((slide, index) => (
          <SwiperSlide key={index} className="carousel__slide">
            {slide}
          </SwiperSlide>
        ))}
        {children}
      </Swiper>
    </div>
  );
};

export default Carousel;