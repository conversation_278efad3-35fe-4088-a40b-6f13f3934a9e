/* Carousel Component Styles */
.carousel {
  position: relative;
  width: 100%;
}

.carousel__swiper {
  width: 100%;
  height: 100%;
}

.carousel__slide {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

/* Navigation Buttons */
.carousel .swiper-button-next,
.carousel .swiper-button-prev {
  width: 3rem;
  height: 3rem;
  background-color: var(--color-white);
  border-radius: var(--border-radius-full);
  box-shadow: var(--shadow-lg);
  color: var(--color-primary);
  transition: all var(--transition-normal) var(--ease-custom);
  border: 1px solid var(--color-border);
}

.carousel .swiper-button-next:hover,
.carousel .swiper-button-prev:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

.carousel .swiper-button-next::after,
.carousel .swiper-button-prev::after {
  font-size: 1rem;
  font-weight: var(--font-weight-bold);
}

.carousel .swiper-button-disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.carousel .swiper-button-disabled:hover {
  background-color: var(--color-white);
  color: var(--color-primary);
  transform: none;
}

/* Pagination */
.carousel .swiper-pagination {
  position: relative;
  margin-top: var(--spacing-4);
}

.carousel .swiper-pagination-bullet {
  width: 0.75rem;
  height: 0.75rem;
  background-color: var(--color-muted);
  opacity: 1;
  transition: all var(--transition-normal) var(--ease-custom);
}

.carousel .swiper-pagination-bullet-active {
  background-color: var(--color-primary);
  transform: scale(1.2);
}

.carousel .swiper-pagination-bullet:hover {
  background-color: var(--color-primary);
  opacity: 0.8;
}

/* Dynamic Bullets */
.carousel .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: all var(--transition-normal) var(--ease-custom);
}

.carousel .swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
  transform: scale(1.4);
}

.carousel .swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev,
.carousel .swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  transform: scale(1.1);
  opacity: 0.7;
}

/* Effect Variants */
.carousel--fade .carousel__slide {
  background-color: transparent;
}

.carousel--slide .carousel__slide {
  /* Default slide styles */
}

/* Responsive Design */
@media (max-width: 768px) {
  .carousel .swiper-button-next,
  .carousel .swiper-button-prev {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .carousel .swiper-button-next::after,
  .carousel .swiper-button-prev::after {
    font-size: 0.875rem;
  }
  
  .carousel .swiper-pagination-bullet {
    width: 0.625rem;
    height: 0.625rem;
  }
}

/* Dark Theme Adjustments */
.theme-dark .carousel .swiper-button-next,
.theme-dark .carousel .swiper-button-prev {
  background-color: var(--color-card-bg);
  border-color: var(--color-border);
}

.theme-dark .carousel .swiper-button-next:hover,
.theme-dark .carousel .swiper-button-prev:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
}

/* Accessibility */
.carousel .swiper-button-next:focus-visible,
.carousel .swiper-button-prev:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .carousel .swiper-button-next,
  .carousel .swiper-button-prev,
  .carousel .swiper-pagination-bullet {
    transition: none;
  }
  
  .carousel .swiper-button-next:hover,
  .carousel .swiper-button-prev:hover {
    transform: none;
  }
}