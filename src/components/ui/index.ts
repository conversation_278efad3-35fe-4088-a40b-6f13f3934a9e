// UI Components Library - shadcn/ui
export { Button, buttonVariants } from "./button";
export { <PERSON>, CardHeader, CardFooter, CardTitle, CardDescription, CardContent } from "./card";
export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from "./tooltip";
export { Badge, badgeVariants } from "./badge";
export { Input } from "./input";

// Legacy UI Components
export { default as LegacyButton } from './Button/index';
export type { ButtonProps as LegacyButtonProps } from './Button/index';

export { default as LegacyCard, CardHeader as LegacyCardHeader, CardBody, CardFooter as LegacyCardFooter } from './Card/index';
export type { CardProps as LegacyCardProps } from './Card/index';

export { default as Carousel } from './Carousel';
export type { CarouselProps } from './Carousel';

export { default as Container } from './Container';
export type { ContainerProps } from './Container';