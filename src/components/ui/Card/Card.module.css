/* Card Component Styles */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: var(--color-card-bg);
  border-radius: var(--border-radius-xl);
  transition: all var(--transition-normal) var(--ease-custom);
  overflow: hidden;
}

/* Card Variants */
.card--default {
  border: 1px solid var(--color-border);
}

.card--elevated {
  border: none;
  box-shadow: var(--shadow-lg);
}

.card--outlined {
  border: 2px solid var(--color-border);
  box-shadow: none;
}

.card--ghost {
  border: none;
  box-shadow: none;
  background-color: transparent;
}

/* Card Padding */
.card--padding-none {
  padding: 0;
}

.card--padding-sm {
  padding: var(--spacing-3);
}

.card--padding-md {
  padding: var(--spacing-4);
}

.card--padding-lg {
  padding: var(--spacing-6);
}

/* Card States */
.card--hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.card--clickable {
  cursor: pointer;
}

.card--clickable:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card--clickable:active {
  transform: translateY(0);
}

/* Card Sub-components */
.card__header {
  padding: var(--spacing-4) var(--spacing-4) var(--spacing-2);
  border-bottom: 1px solid var(--color-border);
}

.card__header:last-child {
  border-bottom: none;
  padding-bottom: var(--spacing-4);
}

.card__body {
  flex: 1;
  padding: var(--spacing-4);
}

.card__header + .card__body {
  padding-top: var(--spacing-2);
}

.card__footer {
  padding: var(--spacing-2) var(--spacing-4) var(--spacing-4);
  border-top: 1px solid var(--color-border);
  margin-top: auto;
}

.card__footer:first-child {
  border-top: none;
  padding-top: var(--spacing-4);
}

/* Card with no padding overrides */
.card--padding-none .card__header,
.card--padding-none .card__body,
.card--padding-none .card__footer {
  padding: var(--spacing-4);
}

.card--padding-sm .card__header,
.card--padding-sm .card__body,
.card--padding-sm .card__footer {
  padding: var(--spacing-3);
}

.card--padding-lg .card__header,
.card--padding-lg .card__body,
.card--padding-lg .card__footer {
  padding: var(--spacing-6);
}

/* Focus States */
.card--clickable:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
