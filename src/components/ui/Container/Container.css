/* Container Component Styles */
.container {
  width: 100%;
  box-sizing: border-box;
}

/* Container Sizes */
.container--sm {
  max-width: 640px;
}

.container--md {
  max-width: 768px;
}

.container--lg {
  max-width: 1024px;
}

.container--xl {
  max-width: 1280px;
}

.container--full {
  max-width: none;
}

/* Container Centering */
.container--center {
  margin-left: auto;
  margin-right: auto;
}

/* Container Padding */
.container--padding {
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

/* Responsive Padding */
@media (min-width: 640px) {
  .container--padding {
    padding-left: var(--spacing-6);
    padding-right: var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .container--padding {
    padding-left: var(--spacing-8);
    padding-right: var(--spacing-8);
  }
}