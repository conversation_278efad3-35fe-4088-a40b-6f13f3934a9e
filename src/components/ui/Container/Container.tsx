import React, { forwardRef } from 'react';
import { CommonProps } from '../../../types/common';
import './Container.css';

export interface ContainerProps extends CommonProps, React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  center?: boolean;
  padding?: boolean;
}

const Container = forwardRef<HTMLDivElement, ContainerProps>(({
  children,
  className = '',
  size = 'lg',
  center = true,
  padding = true,
  ...props
}, ref) => {
  const baseClass = 'container';
  const sizeClass = `container--${size}`;
  const centerClass = center ? 'container--center' : '';
  const paddingClass = padding ? 'container--padding' : '';
  
  const classes = [
    baseClass,
    sizeClass,
    centerClass,
    paddingClass,
    className
  ].filter(Boolean).join(' ');

  return (
    <div
      ref={ref}
      className={classes}
      {...props}
    >
      {children}
    </div>
  );
});

Container.displayName = 'Container';

export default Container;