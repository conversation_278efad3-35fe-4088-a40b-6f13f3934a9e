.about-page {
  padding: 6rem 0;
  min-height: 100vh;
  background-color: var(--color-slate-50);
  background-image: 
    url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3z' fill='%236366f1' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E"),
    linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.05) 100%);
  background-size: 80px 80px, cover;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.about-header {
  text-align: center;
  margin-bottom: 4rem;
}

.about-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 1rem;
}

.about-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
}

.about-content {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.about-section {
  background-color: var(--color-white);
  border-radius: 1rem;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.about-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.75rem;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  border-radius: 3px;
}

.section-content {
  color: var(--color-gray-700);
  line-height: 1.7;
}

.section-content p {
  margin-bottom: 1.25rem;
}

.section-content p:last-child {
  margin-bottom: 0;
}

/* 技能部分 */
.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
}

.skill-category {
  background-color: var(--color-slate-50);
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: transform 0.3s ease;
  border: 1px solid var(--color-slate-200);
}

.skill-category:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
}

.skill-category h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: 1rem;
  text-align: center;
}

.skill-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.skill-list li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--color-gray-700);
}

.skill-list li:last-child {
  margin-bottom: 0;
}

.skill-list li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
}

/* 时间线部分 */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--color-indigo-200), var(--color-purple-200));
}

.timeline-item {
  position: relative;
  padding-bottom: 2.5rem;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-date {
  position: absolute;
  left: -2.5rem;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  transform: translateX(-50%);
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
}

.timeline-content {
  background-color: var(--color-white);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-left: 1rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border-left: 3px solid var(--color-indigo-500);
}

.timeline-content h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: 0.5rem;
}

.timeline-content p {
  color: var(--color-gray-700);
  margin: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .about-page {
    padding: 4rem 0;
  }
  
  .about-title {
    font-size: var(--font-size-3xl);
  }
  
  .about-subtitle {
    font-size: var(--font-size-lg);
  }
  
  .about-section {
    padding: 1.5rem;
  }
  
  .skills-grid {
    grid-template-columns: 1fr;
  }
  
  .timeline-date {
    position: relative;
    left: 0;
    transform: none;
    display: inline-block;
    margin-bottom: 0.75rem;
  }
  
  .timeline-content {
    margin-left: 0;
  }
}
