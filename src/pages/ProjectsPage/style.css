.projects-page {
  padding: 6rem 0;
  min-height: 100vh;
  background-color: var(--color-slate-50);
  background-image: 
    url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3z' fill='%236366f1' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E"),
    linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(168, 85, 247, 0.05) 100%);
  background-size: 80px 80px, cover;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.projects-header {
  text-align: center;
  margin-bottom: 3rem;
}

.projects-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 1rem;
}

.projects-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
}

.projects-filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  gap: 1.5rem;
}

.category-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.category-button {
  padding: 0.5rem 1.25rem;
  border-radius: 2rem;
  background-color: var(--color-white);
  color: var(--color-gray-700);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: 1px solid var(--color-slate-200);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.category-button:hover {
  background-color: var(--color-slate-100);
  color: var(--color-gray-900);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
}

.category-button.active {
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
}

.search-filter {
  position: relative;
  flex-grow: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 2rem;
  border: 1px solid var(--color-slate-200);
  background-color: var(--color-white);
  color: var(--color-gray-900);
  font-size: var(--font-size-sm);
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-indigo-400);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-500);
  font-size: var(--font-size-sm);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2.5rem;
}

.project-card {
  background-color: var(--color-white);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid var(--color-slate-200);
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 不规则形状卡片 */
.projects-grid .project-card:nth-child(3n+1) {
  border-radius: 30px 10px 40px 15px;
}

.projects-grid .project-card:nth-child(3n+2) {
  border-radius: 15px 35px 10px 25px;
}

.projects-grid .project-card:nth-child(3n+3) {
  border-radius: 20px 20px 40px 10px;
}

.project-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.project-image {
  position: relative;
  overflow: hidden;
  height: 220px;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.view-project {
  display: inline-block;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  transform: translateY(20px);
  opacity: 0;
}

.project-card:hover .view-project {
  transform: translateY(0);
  opacity: 1;
  transition: all 0.3s ease 0.1s;
}

.view-project:hover {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.project-info {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.project-info .project-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 0.5rem;
}

.project-info .project-category {
  display: inline-block;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  color: white;
  padding: 0.3rem 0.75rem;
  border-radius: 2rem;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  margin-bottom: 1rem;
  width: fit-content;
}

.project-info .project-description {
  color: var(--color-gray-700);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.project-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.technology-tag {
  background-color: var(--color-slate-100);
  color: var(--color-gray-800);
  padding: 0.3rem 0.6rem;
  border-radius: 0.3rem;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
}

.technology-tag:hover {
  background-color: var(--color-indigo-100);
  color: var(--color-indigo-700);
}

.no-projects {
  text-align: center;
  padding: 4rem 0;
  background-color: var(--color-white);
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-slate-200);
}

.no-projects h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 0.75rem;
}

.no-projects p {
  color: var(--color-gray-600);
  margin-bottom: 1.5rem;
}

.reset-button {
  display: inline-block;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: var(--font-weight-medium);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
}

.reset-button:hover {
  box-shadow: 0 6px 15px rgba(99, 102, 241, 0.4);
  transform: translateY(-2px);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .projects-page {
    padding: 4rem 0;
  }
  
  .projects-title {
    font-size: var(--font-size-3xl);
  }
  
  .projects-subtitle {
    font-size: var(--font-size-lg);
  }
  
  .projects-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-filter {
    max-width: 100%;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .category-filters {
    justify-content: center;
  }
}
