.home-page {
  min-height: 100vh;
  background-color: var(--color-slate-50);
}

/* 英雄区域样式 */
.hero-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  overflow: hidden;
  background-color: var(--color-slate-900);
  color: white;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.4) 0%, transparent 30%),
    radial-gradient(circle at 80% 70%, rgba(168, 85, 247, 0.4) 0%, transparent 30%);
  z-index: 1;
}

.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  opacity: 0.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
  position: relative;
  z-index: 10;
}

.hero-content {
  max-width: 700px;
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-indigo-300);
  margin-bottom: 1.5rem;
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--color-indigo-300), var(--color-purple-300));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.hero-description {
  font-size: var(--font-size-xl);
  color: var(--color-slate-300);
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.btn-primary {
  display: inline-block;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
  border: none;
  cursor: pointer;
  font-size: var(--font-size-base);
}

.btn-primary:hover {
  box-shadow: 0 6px 15px rgba(99, 102, 241, 0.4);
  transform: translateY(-2px);
}

.btn-secondary {
  display: inline-block;
  background-color: transparent;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid var(--color-indigo-400);
  cursor: pointer;
  font-size: var(--font-size-base);
}

.btn-secondary:hover {
  background-color: rgba(99, 102, 241, 0.1);
  transform: translateY(-2px);
}

.scroll-down {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--color-slate-300);
  font-size: var(--font-size-sm);
  text-decoration: none;
  transition: color 0.3s ease;
  z-index: 10;
}

.scroll-down:hover {
  color: white;
}

.scroll-down i {
  font-size: 1.5rem;
  margin-top: 0.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 关于我区域样式 */
.about-section {
  padding: 6rem 0;
  background-color: var(--color-white);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
  padding-bottom: 1rem;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  border-radius: 3px;
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  max-width: 700px;
  margin: 0 auto;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.about-image {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.about-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.about-image:hover img {
  transform: scale(1.05);
}

.about-image::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  border-radius: 1rem;
  z-index: -1;
}

.about-image::after {
  content: '';
  position: absolute;
  bottom: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  border-radius: 1rem;
  z-index: -1;
}

.about-text h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 1.5rem;
}

.about-text p {
  color: var(--color-gray-700);
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.about-text p:last-child {
  margin-bottom: 2rem;
}

/* 我的世界区域样式 */
.my-world-section {
  padding: 6rem 0;
  background-color: var(--color-slate-50);
  background-image: 
    url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3z' fill='%236366f1' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E"),
    linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(168, 85, 247, 0.05) 100%);
  background-size: 80px 80px, cover;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.card {
  background-color: var(--color-white);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid var(--color-slate-200);
  position: relative;
  overflow: hidden;
}

/* 不规则形状卡片 */
.card:nth-child(1) {
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  height: 350px;
}

.card:nth-child(2) {
  border-radius: 10% 90% 10% 90% / 90% 10% 90% 10%;
}

.card:nth-child(3) {
  border-radius: 50% 50% 20% 80% / 25% 80% 20% 75%;
}

.card:nth-child(4) {
  border-radius: 80% 20% 50% 50% / 50% 40% 60% 50%;
}

.card:nth-child(5) {
  border-radius: 20% 80% 30% 70% / 70% 30% 70% 30%;
}

.card:nth-child(6) {
  border-radius: 60% 40% 50% 50% / 30% 30% 70% 70%;
}

.card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.card-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

.card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 1rem;
}

.card p {
  color: var(--color-gray-700);
  line-height: 1.6;
}

/* 项目展示区域样式 */
.projects-section {
  padding: 6rem 0;
  background-color: var(--color-white);
}

/* 注意：项目卡片的详细样式已移至 ProjectsSection.css */

.view-all-projects {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.view-all-projects {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

/* 联系区域样式 */
.contact-section {
  padding: 6rem 0;
  background-color: var(--color-slate-900);
  color: white;
  position: relative;
  overflow: hidden;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.2) 0%, transparent 30%),
    radial-gradient(circle at 80% 70%, rgba(168, 85, 247, 0.2) 0%, transparent 30%);
  z-index: 1;
}

.contact-section .container {
  position: relative;
  z-index: 10;
}

.contact-section .section-title {
  color: white;
}

.contact-section .section-subtitle {
  color: var(--color-slate-300);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contact-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
}

.contact-text h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: white;
  margin-bottom: 0.5rem;
}

.contact-text p, .contact-text a {
  color: var(--color-slate-300);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-text a:hover {
  color: white;
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1.25rem;
  transition: all 0.3s ease;
  position: relative;
}

.social-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.social-link:hover {
  transform: translateY(-3px);
}

.social-link:hover::after {
  width: 100%;
}

.contact-form {
  background-color: var(--color-white);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-slate-300);
  border-radius: 0.5rem;
  background-color: var(--color-white);
  color: var(--color-gray-900);
  font-size: var(--font-size-base);
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-indigo-400);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
  min-height: 150px;
  resize: vertical;
}

.form-submit {
  display: block;
  width: 100%;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
  font-size: var(--font-size-base);
}

.form-submit:hover {
  box-shadow: 0 6px 15px rgba(99, 102, 241, 0.4);
  transform: translateY(-2px);
}

/* 视差效果按钮 */
.parallax-button {
  position: fixed;
  top: 80%;
  right: 2rem;
  z-index: 100;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.5);
  border: none;
}

.parallax-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(99, 102, 241, 0.7);
}

.parallax-button.active {
  background: linear-gradient(135deg, var(--color-purple-600), var(--color-indigo-600));
}

/* 响应式调整 */
@media (max-width: 992px) {
  .about-content,
  .contact-content {
    grid-template-columns: 1fr;
  }
  
  .about-image {
    margin-bottom: 2rem;
  }
  
  .cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: var(--font-size-4xl);
  }
  
  .hero-description {
    font-size: var(--font-size-lg);
  }
  
  .section-title {
    font-size: var(--font-size-2xl);
  }
  
  .cards-grid {
    grid-template-columns: 1fr;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
  }
  
  .parallax-button {
    right: 1rem;
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
}
