import React from 'react';
import { motion } from 'framer-motion';
import { FiBook<PERSON>pen, FiEdit, FiPlus } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import PageAnimation from '../../components/common/PageAnimation/PageAnimation';
import { BlogList } from '../../components/blog';
import { initializeSampleBlogs } from '../../data/sampleBlogs';
import './BlogsPage.css';

const BlogsPage: React.FC = () => {
  // Initialize sample blogs on component mount
  React.useEffect(() => {
    initializeSampleBlogs();
  }, []);

  return (
    <PageAnimation type="fade-in" duration={0.8} className="blogs-page">
      {/* Hero Section */}
      <motion.section
        className="blogs-page__hero"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="hero-content">
          <div className="hero-badge">
            <FiBookOpen className="badge-icon" />
            <span>Blog</span>
          </div>
          
          <h1 className="hero-title">
            Explore Our Blog
          </h1>
          
          <p className="hero-subtitle">
            Discover insights, tutorials, and stories about web development, 
            design, and technology. Stay updated with the latest trends and best practices.
          </p>
          
          <div className="hero-actions">
            <Link to="/blog-editor" className="action-btn action-btn--primary">
              <FiPlus />
              <span>Write New Post</span>
            </Link>
            <Link to="/blog-editor" className="action-btn action-btn--secondary">
              <FiEdit />
              <span>Manage Posts</span>
            </Link>
          </div>
        </div>
        
        {/* Background decoration */}
        <div className="hero-decoration">
          <div className="decoration-shape decoration-shape--1"></div>
          <div className="decoration-shape decoration-shape--2"></div>
          <div className="decoration-shape decoration-shape--3"></div>
        </div>
      </motion.section>

      {/* Blog List Section */}
      <motion.section
        className="blogs-page__content"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <BlogList
          showSearch={true}
          showFilters={true}
          showPagination={true}
          pageSize={12}
          viewMode="grid"
        />
      </motion.section>
    </PageAnimation>
  );
};

export default BlogsPage;
