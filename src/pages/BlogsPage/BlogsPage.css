.blogs-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow-x: hidden;
}

/* Animated background elements */
.blogs-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

/* Hero Section */
.blogs-page__hero {
  position: relative;
  padding: 5rem 0 8rem;
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.1) 0%,
    rgba(168, 85, 247, 0.1) 50%,
    rgba(236, 72, 153, 0.1) 100%);
  overflow: hidden;
}

.blogs-page__hero::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 30% 70%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(168, 85, 247, 0.15) 0%, transparent 50%);
  animation: heroFloat 15s ease-in-out infinite;
  pointer-events: none;
}

@keyframes heroFloat {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.hero-content {
  max-width: 64rem;
  margin: 0 auto;
  padding: 0 1rem;
  text-align: center;
  position: relative;
  z-index: 10;
}

@media (min-width: 640px) {
  .hero-content {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .hero-content {
    padding: 0 2rem;
  }
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(99, 102, 241, 0.2);
  animation: badgeGlow 3s ease-in-out infinite;
}

@keyframes badgeGlow {
  0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
  50% { box-shadow: 0 0 30px rgba(99, 102, 241, 0.5); }
}

.badge-icon {
  width: 1rem;
  height: 1rem;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleSlide 1s ease-out;
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: 3.75rem;
  }
}

@keyframes titleSlide {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #4b5563;
  margin-bottom: 2.5rem;
  max-width: 48rem;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  animation: subtitleFade 1s ease-out 0.3s both;
}

@media (min-width: 1024px) {
  .hero-subtitle {
    font-size: 1.5rem;
  }
}

@keyframes subtitleFade {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  animation: actionsSlide 1s ease-out 0.6s both;
}

@media (min-width: 640px) {
  .hero-actions {
    flex-direction: row;
  }
}

@keyframes actionsSlide {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.action-btn--primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.action-btn--primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.action-btn--secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  border: 1px solid rgba(209, 213, 219, 0.5);
  backdrop-filter: blur(10px);
}

.action-btn--secondary:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(156, 163, 175, 0.5);
}

/* Content Section */
.blogs-page__content {
  padding: 4rem 0;
  position: relative;
  z-index: 5;
}

/* Floating decoration elements */
.blogs-page__content::before {
  content: '';
  position: absolute;
  top: 10%;
  right: 10%;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(168, 85, 247, 0.1));
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.blogs-page__content::after {
  content: '';
  position: absolute;
  bottom: 20%;
  left: 5%;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.1), rgba(251, 146, 60, 0.1));
  border-radius: 50%;
  animation: float 8s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(120deg);
  }
  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .blogs-page__hero {
    padding: 4rem 0 6rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.125rem;
  }

  .hero-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .blogs-page__content {
    padding: 3rem 0;
  }
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .blogs-page {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }

  .blogs-page::before {
    background:
      radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.05) 0%, transparent 50%);
  }

  .blogs-page__hero {
    background: linear-gradient(135deg,
      rgba(99, 102, 241, 0.05) 0%,
      rgba(168, 85, 247, 0.05) 50%,
      rgba(236, 72, 153, 0.05) 100%);
  }

  .hero-badge {
    background: rgba(99, 102, 241, 0.15);
    color: #a5b4fc;
    border-color: rgba(99, 102, 241, 0.3);
  }

  .hero-title {
    background: linear-gradient(135deg, #a5b4fc 0%, #c4b5fd 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .hero-subtitle {
    color: #9ca3af;
  }

  .action-btn--secondary {
    background: rgba(31, 41, 55, 0.8);
    color: #f3f4f6;
    border-color: rgba(75, 85, 99, 0.5);
  }

  .action-btn--secondary:hover {
    background: rgba(31, 41, 55, 0.9);
    border-color: rgba(107, 114, 128, 0.5);
  }
}

/* Additional dark mode class support */
.blogs-page.dark {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.blogs-page.dark .hero-title {
  background: linear-gradient(135deg, #a5b4fc 0%, #c4b5fd 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.blogs-page.dark .hero-subtitle {
  color: #9ca3af;
}

.blogs-page.dark .hero-badge {
  background: rgba(99, 102, 241, 0.15);
  color: #a5b4fc;
  border-color: rgba(99, 102, 241, 0.3);
}

.blogs-page.dark .action-btn--secondary {
  background: rgba(31, 41, 55, 0.8);
  color: #f3f4f6;
  border-color: rgba(75, 85, 99, 0.5);
}

/* Focus states for accessibility */
.action-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .blogs-page::before,
  .blogs-page__hero::before,
  .blogs-page__content::before,
  .blogs-page__content::after {
    display: none;
  }

  .blogs-page {
    background: white;
  }

  .blogs-page__hero {
    background: white;
    padding: 2rem 0;
  }

  .hero-actions {
    display: none;
  }

  .hero-title,
  .hero-subtitle {
    color: black !important;
    background: none !important;
    -webkit-text-fill-color: black !important;
  }
}
