.blog-view-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow-x: hidden;
}

/* Animated background elements */
.blog-view-page::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.05) 0%, transparent 50%);
  animation: backgroundPulse 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes backgroundPulse {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

.blog-view-page.fullscreen {
  position: fixed;
  inset: 0;
  z-index: 50;
  overflow: auto;
}

.blog-view-page.dark-mode {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.blog-view-page.dark-mode::before {
  background: radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.03) 0%, transparent 50%);
}

/* Header */
.blog-view-page__header {
  position: sticky;
  top: 0;
  z-index: 40;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  transition: all 0.3s ease;
  animation: headerSlide 0.8s ease-out;
}

@keyframes headerSlide {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.blog-view-page.dark-mode .blog-view-page__header {
  background: rgba(15, 23, 42, 0.9);
  border-bottom-color: rgba(51, 65, 85, 0.5);
}

.header-content {
  max-width: 64rem;
  margin: 0 auto;
  padding: 1rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 10;
}

@media (min-width: 640px) {
  .header-content {
    padding: 1rem 1.5rem;
  }
}

@media (min-width: 1024px) {
  .header-content {
    padding: 1rem 2rem;
  }
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #4b5563;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  position: relative;
  overflow: hidden;
}

.back-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left 0.5s ease;
}

.back-link:hover::before {
  left: 100%;
}

.back-link:hover {
  color: #1f2937;
  background: rgba(99, 102, 241, 0.05);
  transform: translateX(-3px);
}

.blog-view-page.dark-mode .back-link {
  color: #d1d5db;
}

.blog-view-page.dark-mode .back-link:hover {
  color: #f9fafb;
  background: rgba(99, 102, 241, 0.1);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn:hover {
  color: #1f2937;
  background: rgba(107, 114, 128, 0.1);
  transform: translateY(-1px);
}

.action-btn.active {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.blog-view-page.dark-mode .action-btn {
  color: #d1d5db;
}

.blog-view-page.dark-mode .action-btn:hover {
  color: #f9fafb;
  background: rgba(75, 85, 99, 0.2);
}

.blog-view-page.dark-mode .action-btn.active {
  color: #60a5fa;
  background: rgba(59, 130, 246, 0.2);
}

/* Article */
.blog-view-page__article {
  max-width: 64rem;
  margin: 0 auto;
  padding: 3rem 1rem;
  position: relative;
  z-index: 10;
  animation: articleFade 1s ease-out;
}

@media (min-width: 640px) {
  .blog-view-page__article {
    padding: 3rem 1.5rem;
  }
}

@media (min-width: 1024px) {
  .blog-view-page__article {
    padding: 3rem 2rem;
  }
}

@keyframes articleFade {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hero */
.article-hero {
  margin-bottom: 3rem;
  animation: heroSlide 1.2s ease-out;
}

@keyframes heroSlide {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.article-thumbnail {
  aspect-ratio: 16/9;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 2rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  position: relative;
  animation: thumbnailZoom 1.5s ease-out;
}

@keyframes thumbnailZoom {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.article-thumbnail::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.article-thumbnail:hover::before {
  opacity: 1;
}

.article-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.article-thumbnail:hover img {
  transform: scale(1.05);
}

.article-header {
  text-align: center;
  animation: headerContent 1.5s ease-out 0.3s both;
}

@keyframes headerContent {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.article-category {
  display: inline-block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(168, 85, 247, 0.1));
  color: #6366f1;
  border-radius: 9999px;
  margin-bottom: 1rem;
  border: 1px solid rgba(99, 102, 241, 0.2);
  animation: categoryGlow 2s ease-in-out infinite;
}

@keyframes categoryGlow {
  0%,
  100% {
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.2);
  }
  50% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
  }
}

.blog-view-page.dark-mode .article-category {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(168, 85, 247, 0.2));
  color: #a5b4fc;
  border-color: rgba(99, 102, 241, 0.3);
}

.article-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@media (min-width: 1024px) {
  .article-title {
    font-size: 3rem;
  }
}

.blog-view-page.dark-mode .article-title {
  background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.article-excerpt {
  font-size: 1.25rem;
  color: #4b5563;
  margin-bottom: 2rem;
  line-height: 1.6;
  max-width: 48rem;
  margin-left: auto;
  margin-right: auto;
}

.blog-view-page.dark-mode .article-excerpt {
  color: #d1d5db;
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  color: #6b7280;
}

.blog-view-page.dark-mode .article-meta {
  color: #9ca3af;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(229, 231, 235, 0.3);
  transition: all 0.3s ease;
}

.meta-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
}

.blog-view-page.dark-mode .meta-item {
  background: rgba(31, 41, 55, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
}

.blog-view-page.dark-mode .meta-item:hover {
  background: rgba(31, 41, 55, 0.8);
}

.meta-icon {
  width: 1rem;
  height: 1rem;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  background: rgba(243, 244, 246, 0.8);
  color: #374151;
  border-radius: 9999px;
  transition: all 0.3s ease;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.tag:hover {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border-color: rgba(99, 102, 241, 0.3);
  transform: translateY(-1px);
}

.blog-view-page.dark-mode .tag {
  background: rgba(31, 41, 55, 0.8);
  color: #d1d5db;
  border-color: rgba(75, 85, 99, 0.5);
}

.blog-view-page.dark-mode .tag:hover {
  background: rgba(99, 102, 241, 0.2);
  color: #a5b4fc;
  border-color: rgba(99, 102, 241, 0.4);
}

/* Content */
.article-content {
  @apply prose prose-lg dark:prose-invert max-w-none mb-12;

  /* Custom prose styles */
  --tw-prose-body: theme("colors.gray.700");
  --tw-prose-headings: theme("colors.gray.900");
  --tw-prose-lead: theme("colors.gray.600");
  --tw-prose-links: theme("colors.blue.600");
  --tw-prose-bold: theme("colors.gray.900");
  --tw-prose-counters: theme("colors.gray.500");
  --tw-prose-bullets: theme("colors.gray.300");
  --tw-prose-hr: theme("colors.gray.200");
  --tw-prose-quotes: theme("colors.gray.900");
  --tw-prose-quote-borders: theme("colors.gray.200");
  --tw-prose-captions: theme("colors.gray.500");
  --tw-prose-code: theme("colors.gray.900");
  --tw-prose-pre-code: theme("colors.gray.200");
  --tw-prose-pre-bg: theme("colors.gray.800");
  --tw-prose-th-borders: theme("colors.gray.300");
  --tw-prose-td-borders: theme("colors.gray.200");
}

.dark .article-content {
  --tw-prose-body: theme("colors.gray.300");
  --tw-prose-headings: theme("colors.white");
  --tw-prose-lead: theme("colors.gray.400");
  --tw-prose-links: theme("colors.blue.400");
  --tw-prose-bold: theme("colors.white");
  --tw-prose-counters: theme("colors.gray.400");
  --tw-prose-bullets: theme("colors.gray.600");
  --tw-prose-hr: theme("colors.gray.700");
  --tw-prose-quotes: theme("colors.gray.100");
  --tw-prose-quote-borders: theme("colors.gray.700");
  --tw-prose-captions: theme("colors.gray.400");
  --tw-prose-code: theme("colors.white");
  --tw-prose-pre-code: theme("colors.gray.300");
  --tw-prose-pre-bg: theme("colors.gray.800");
  --tw-prose-th-borders: theme("colors.gray.600");
  --tw-prose-td-borders: theme("colors.gray.700");
}

/* Article Actions */
.article-actions {
  @apply flex flex-wrap items-center justify-center gap-4 py-8 
         border-t border-b border-gray-200 dark:border-gray-700 mb-12;
}

.share-container {
  @apply relative;
}

.share-menu {
  @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 
         bg-white dark:bg-gray-800 rounded-lg shadow-lg border 
         border-gray-200 dark:border-gray-700 py-2 min-w-[120px];
}

.share-menu button {
  @apply w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 
         hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

.share-overlay {
  @apply fixed inset-0 z-30;
}

/* Related Posts */
.blog-view-page__related {
  @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12 
         border-t border-gray-200 dark:border-gray-700;
}

.blog-view-page__related h2 {
  @apply text-3xl font-bold text-gray-900 dark:text-white text-center mb-12;
}

.related-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
}

/* Navigation */
.blog-view-page__navigation {
  @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center;
}

.nav-link {
  @apply inline-flex items-center gap-2 px-6 py-3 bg-blue-600 
         hover:bg-blue-700 text-white font-medium rounded-xl 
         transition-all duration-300 hover:scale-105 hover:shadow-lg;
}

/* Loading and Error States */
.blog-view-page__loading,
.blog-view-page__error {
  @apply flex flex-col items-center justify-center min-h-screen 
         text-gray-500 dark:text-gray-400;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full mb-4;
}

.blog-view-page__error h1 {
  @apply text-3xl font-bold text-gray-900 dark:text-white mb-4;
}

.blog-view-page__error p {
  @apply text-lg mb-8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .article-title {
    @apply text-3xl;
  }

  .article-excerpt {
    @apply text-lg;
  }

  .article-meta {
    @apply flex-col gap-3;
  }

  .article-actions {
    @apply flex-col;
  }

  .header-actions {
    @apply gap-2;
  }

  .action-btn span {
    @apply hidden;
  }
}

/* Print Styles */
@media print {
  .blog-view-page__header,
  .article-actions,
  .blog-view-page__related,
  .blog-view-page__navigation {
    display: none;
  }

  .blog-view-page {
    @apply bg-white text-black;
  }

  .article-content {
    @apply text-black;
  }
}

/* Focus states for accessibility */
.action-btn:focus,
.back-link:focus,
.nav-link:focus {
  @apply ring-2 ring-blue-500 ring-offset-2 ring-offset-white 
         dark:ring-offset-gray-900;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
.blog-view-page::-webkit-scrollbar {
  width: 8px;
}

.blog-view-page::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

.blog-view-page::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.blog-view-page::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}
