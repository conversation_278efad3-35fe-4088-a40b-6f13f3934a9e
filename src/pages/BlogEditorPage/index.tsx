import React, { useState, useCallback, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import ReactMarkdown from "react-markdown";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { tomorrow, prism } from "react-syntax-highlighter/dist/esm/styles/prism";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { useParams, useNavigate } from "react-router-dom";
import {
  FiSave,
  FiEye,
  FiEdit3,
  FiImage,
  FiTable,
  FiCode,
  FiLink,
  FiList,
  FiClock,
  FiTag,
  FiBold,
  FiItalic,
  FiMessageSquare,
  FiMinus,
  FiCheck,
  FiUpload,
  FiDownload,
  FiRefreshCw,
  FiHash,
  FiMaximize2,
  FiMinimize2,
  FiCopy,
  FiZap,
  FiUser,
  FiFileText,
  FiGlobe,
  FiArchive,
  FiAlertCircle,
} from "react-icons/fi";
import PageAnimation from "../../components/common/PageAnimation/PageAnimation";
import Tooltip from "../../components/common/Tooltip/Tooltip";
import { useBlogPost } from "../../hooks/useBlogPost";
import { BlogPost } from "../../types/common";
import "./BlogEditorPage.css";

const BlogEditorPage: React.FC = () => {
  const { blogId } = useParams<{ blogId?: string }>();
  const navigate = useNavigate();

  // Blog management hook
  const {
    blog,
    hasUnsavedChanges,
    isLoading,
    isSaving,
    error,
    lastSaved,
    loadBlog,
    saveBlog,
    createNewBlog,
    updateTitle,
    updateContent,
    updateExcerpt,
    updateAuthor,
    addTag,
    removeTag,
    updateCategory,
    updateThumbnail,
    updateStatus,
    publishBlog,
    unpublishBlog,
    archiveBlog,
    addModification,
    resetChanges,
    clearError,
    getWordCount,
    getCharacterCount,
    getReadingTime
  } = useBlogPost({
    blogId,
    autoSave: true,
    autoSaveInterval: 30000,
    enableHistory: true
  });

  // UI state
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [newTag, setNewTag] = useState("");
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showMetadataPanel, setShowMetadataPanel] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Initialize blog on component mount
  useEffect(() => {
    if (!blogId) {
      // Create new blog if no ID provided
      createNewBlog({
        title: "",
        content: '# Welcome to Blog Editor\n\nStart writing your amazing content here...\n\n## Features\n\n- **Real-time preview** with syntax highlighting\n- **Rich toolbar** with quick insert tools\n- **Tag management** system\n- **Auto-save** functionality\n- **Modification history** tracking\n\n### Code Example\n\n```javascript\nconst greeting = "Hello, World!";\nconsole.log(greeting);\n```\n\n### Table Example\n\n| Feature | Status | Priority |\n|---------|--------|---------|\n| Editor | ✅ Done | High |\n| Preview | ✅ Done | High |\n| Tags | ✅ Done | Medium |\n\n> This is a blockquote example. Perfect for highlighting important information!\n\n---\n\nHappy writing! 🚀',
        tags: ["markdown", "editor", "blog"],
        author: "Anonymous"
      });
    }
  }, [blogId, createNewBlog]);

  // Clear errors when they occur
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  const handleContentChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateContent(e.target.value);
  }, [updateContent]);

  const handleTitleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateTitle(e.target.value);
  }, [updateTitle]);

  const handleExcerptChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateExcerpt(e.target.value);
  }, [updateExcerpt]);

  const handleAuthorChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateAuthor(e.target.value);
  }, [updateAuthor]);

  const handleCategoryChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateCategory(e.target.value);
  }, [updateCategory]);

  const handleThumbnailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateThumbnail(e.target.value);
  }, [updateThumbnail]);

  const handleAddTag = useCallback(() => {
    if (newTag.trim() && blog && !blog.tags.includes(newTag.trim())) {
      addTag(newTag.trim());
      setNewTag("");
    }
  }, [newTag, blog, addTag]);

  const handleRemoveTag = useCallback((tagToRemove: string) => {
    removeTag(tagToRemove);
  }, [removeTag]);

  const insertAtCursor = useCallback(
    (text: string) => {
      const textarea = textareaRef.current;
      if (!textarea || !blog) return;

      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const content = blog.content;

      const newContent = content.substring(0, start) + text + content.substring(end);
      updateContent(newContent);

      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + text.length, start + text.length);
      }, 0);
    },
    [blog, updateContent]
  );

  // Toolbar functions
  const insertHeading = useCallback(() => insertAtCursor("\n## Heading\n"), [insertAtCursor]);
  const insertBold = useCallback(() => insertAtCursor("**bold text**"), [insertAtCursor]);
  const insertItalic = useCallback(() => insertAtCursor("*italic text*"), [insertAtCursor]);
  const insertQuote = useCallback(() => insertAtCursor("\n> Quote text\n"), [insertAtCursor]);
  const insertHorizontalRule = useCallback(() => insertAtCursor("\n---\n"), [insertAtCursor]);
  const insertList = useCallback(
    () => insertAtCursor("\n- Item 1\n- Item 2\n- Item 3\n"),
    [insertAtCursor]
  );
  const insertLink = useCallback(
    () => insertAtCursor("[Link text](https://example.com)"),
    [insertAtCursor]
  );
  const insertImage = useCallback(() => insertAtCursor("![Alt text](image-url)"), [insertAtCursor]);
  const insertCodeBlock = useCallback(
    () => insertAtCursor('\n```javascript\n// Your code here\nconsole.log("Hello World!");\n```\n'),
    [insertAtCursor]
  );

  const insertTable = useCallback(() => {
    const tableText = `
| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |
`;
    insertAtCursor(tableText);
  }, [insertAtCursor]);

  const handleSaveBlog = useCallback(async () => {
    if (!blog) return;

    const result = await saveBlog();
    if (result.success) {
      addModification("Blog post saved manually", blog.author);
    }
  }, [blog, saveBlog, addModification]);

  const handlePublishBlog = useCallback(async () => {
    if (!blog) return;

    publishBlog();
    await saveBlog();
  }, [blog, publishBlog, saveBlog]);

  const handleUnpublishBlog = useCallback(async () => {
    if (!blog) return;

    unpublishBlog();
    await saveBlog();
  }, [blog, unpublishBlog, saveBlog]);

  const handleArchiveBlog = useCallback(async () => {
    if (!blog) return;

    archiveBlog();
    await saveBlog();
  }, [blog, archiveBlog, saveBlog]);

  const exportMarkdown = useCallback(() => {
    if (!blog) return;

    const element = document.createElement("a");
    const file = new Blob([blog.content], { type: "text/markdown" });
    element.href = URL.createObjectURL(file);
    element.download = `${blog.title || "blog-post"}.md`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  }, [blog]);

  const importMarkdown = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && blog) {
      const reader = new FileReader();
      reader.onload = e => {
        const content = e.target?.result as string;
        updateContent(content);
        addModification(`Imported content from ${file.name}`, blog.author);
      };
      reader.readAsText(file);
    }
  }, [blog, updateContent, addModification]);

  const copyToClipboard = useCallback(async () => {
    if (!blog) return;

    try {
      await navigator.clipboard.writeText(blog.content);
      // You could add a toast notification here
    } catch (err) {
      console.error("Failed to copy content:", err);
    }
  }, [blog]);

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  const handleKeyboardShortcuts = useCallback(
    (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case "s":
            e.preventDefault();
            handleSaveBlog();
            break;
          case "b":
            e.preventDefault();
            insertBold();
            break;
          case "i":
            e.preventDefault();
            insertItalic();
            break;
          case "k":
            e.preventDefault();
            insertLink();
            break;
        }
      }
    },
    [handleSaveBlog, insertBold, insertItalic, insertLink]
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeyboardShortcuts);
    return () => document.removeEventListener("keydown", handleKeyboardShortcuts);
  }, [handleKeyboardShortcuts]);

  // Show loading state
  if (isLoading) {
    return (
      <PageAnimation type="fade-in" duration={0.8} className="blog-editor">
        <div className="blog-editor__loading">
          <FiRefreshCw className="icon spinning" />
          <span>Loading blog...</span>
        </div>
      </PageAnimation>
    );
  }

  // Show error state
  if (error) {
    return (
      <PageAnimation type="fade-in" duration={0.8} className="blog-editor">
        <div className="blog-editor__error">
          <FiAlertCircle className="icon" />
          <span>{error}</span>
          <button onClick={clearError}>Dismiss</button>
        </div>
      </PageAnimation>
    );
  }

  // Show empty state if no blog
  if (!blog) {
    return (
      <PageAnimation type="fade-in" duration={0.8} className="blog-editor">
        <div className="blog-editor__empty">
          <FiFileText className="icon" />
          <span>No blog found</span>
          <button onClick={() => navigate('/blog-editor')}>Create New Blog</button>
        </div>
      </PageAnimation>
    );
  }

  return (
    <PageAnimation
      type="fade-in"
      duration={0.8}
      className={`blog-editor ${isDarkMode ? "theme-dark" : ""} ${
        isFullscreen ? "fullscreen" : ""
      }`}
    >
      {/* Error notification */}
      <AnimatePresence>
        {error && (
          <motion.div
            className="blog-editor__error-notification"
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
          >
            <FiAlertCircle className="icon" />
            <span>{error}</span>
            <button onClick={clearError}>×</button>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        className="blog-editor__header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <div className="blog-editor__title-section">
          <motion.input
            type="text"
            placeholder="Enter your blog title..."
            title="Enter a compelling title for your blog post. This will be used as the filename when exporting."
            value={blog.title}
            onChange={handleTitleChange}
            className="blog-editor__title-input"
            whileFocus={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          />
          <motion.div
            className="blog-editor__meta"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <div className="blog-editor__created-at">
              <FiClock className="icon" />
              <span>Created: {blog.createdAt.toLocaleDateString()}</span>
            </div>
            <div className="blog-editor__stats">
              <motion.span
                key={getWordCount()}
                initial={{ scale: 1.2, color: "var(--color-primary)" }}
                animate={{ scale: 1, color: "var(--color-gray-500)" }}
                transition={{ duration: 0.3 }}
              >
                {getWordCount()} words
              </motion.span>
              <span>•</span>
              <motion.span
                key={getCharacterCount()}
                initial={{ scale: 1.2, color: "var(--color-primary)" }}
                animate={{ scale: 1, color: "var(--color-gray-500)" }}
                transition={{ duration: 0.3 }}
              >
                {getCharacterCount()} characters
              </motion.span>
              <span>•</span>
              <span>{getReadingTime()} min read</span>
            </div>
            <div className="blog-editor__status">
              <span className={`status-badge status-${blog.status}`}>
                {blog.status.charAt(0).toUpperCase() + blog.status.slice(1)}
              </span>
              {hasUnsavedChanges && (
                <span className="unsaved-indicator">• Unsaved changes</span>
              )}
            </div>
            <AnimatePresence>
              {isSaving && (
                <motion.div
                  className="blog-editor__auto-save"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 10 }}
                >
                  <FiRefreshCw className="icon spinning" />
                  <span>Saving...</span>
                </motion.div>
              )}
              {lastSaved && !isSaving && (
                <motion.div
                  className="blog-editor__last-saved"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                >
                  <FiCheck className="icon" />
                  <span>Saved {lastSaved.toLocaleTimeString()}</span>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>

        <div className="blog-editor__actions">
          {/* Primary actions - most important */}
          <motion.button
            onClick={handleSaveBlog}
            className="blog-editor__save"
            disabled={isSaving}
            whileHover={{
              scale: 1.05,
              boxShadow: "0 8px 25px rgba(var(--color-primary-rgb), 0.3)",
            }}
            whileTap={{ scale: 0.95 }}
          >
            {isSaving ? <FiRefreshCw className="spinning" /> : <FiSave />}
            <span>{isSaving ? 'Saving...' : 'Save'}</span>
          </motion.button>

          {/* Publishing actions */}
          {blog.status === 'draft' && (
            <motion.button
              onClick={handlePublishBlog}
              className="blog-editor__publish"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FiGlobe />
              <span>Publish</span>
            </motion.button>
          )}

          {blog.status === 'published' && (
            <motion.button
              onClick={handleUnpublishBlog}
              className="blog-editor__unpublish"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <FiEdit3 />
              <span>Unpublish</span>
            </motion.button>
          )}

          <motion.button
            onClick={handleArchiveBlog}
            className="blog-editor__archive"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FiArchive />
            <span>Archive</span>
          </motion.button>

          <motion.button
            onClick={() => setIsPreviewMode(!isPreviewMode)}
            className={`blog-editor__toggle ${isPreviewMode ? "active" : ""}`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {isPreviewMode ? <FiEdit3 /> : <FiEye />}
            <span>{isPreviewMode ? "Edit" : "Preview"}</span>
          </motion.button>

          {/* Metadata toggle */}
          <motion.button
            onClick={() => setShowMetadataPanel(!showMetadataPanel)}
            className={`blog-editor__metadata-toggle ${showMetadataPanel ? "active" : ""}`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FiUser />
            <span>Meta</span>
          </motion.button>

          {/* Secondary actions - utility functions */}
          <motion.button
            onClick={toggleFullscreen}
            className="blog-editor__import-btn"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {isFullscreen ? <FiMinimize2 /> : <FiMaximize2 />}
            <span>Full</span>
          </motion.button>

          <motion.button
            onClick={copyToClipboard}
            className="blog-editor__import-btn"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FiCopy />
            <span>Copy</span>
          </motion.button>

          {/* File operations */}
          <motion.label
            className="blog-editor__import-btn"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FiUpload />
            <span>Import</span>
            <input
              type="file"
              accept=".md,.txt"
              onChange={importMarkdown}
              style={{ display: "none" }}
            />
          </motion.label>

          <motion.button
            onClick={exportMarkdown}
            className="blog-editor__export"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FiDownload />
            <span>Export</span>
          </motion.button>
        </div>
      </motion.div>

      {/* Metadata Panel */}
      <AnimatePresence>
        {showMetadataPanel && (
          <motion.div
            className="blog-editor__metadata-panel"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="blog-editor__metadata-grid">
              <div className="blog-editor__metadata-field">
                <label>Author</label>
                <input
                  type="text"
                  value={blog.author}
                  onChange={handleAuthorChange}
                  placeholder="Author name"
                />
              </div>
              <div className="blog-editor__metadata-field">
                <label>Category</label>
                <input
                  type="text"
                  value={blog.category || ''}
                  onChange={handleCategoryChange}
                  placeholder="Blog category"
                />
              </div>
              <div className="blog-editor__metadata-field">
                <label>Thumbnail URL</label>
                <input
                  type="text"
                  value={blog.thumbnail || ''}
                  onChange={handleThumbnailChange}
                  placeholder="https://example.com/image.jpg"
                />
              </div>
              <div className="blog-editor__metadata-field">
                <label>Excerpt</label>
                <textarea
                  value={blog.excerpt}
                  onChange={handleExcerptChange}
                  placeholder="Brief description of the blog post..."
                  rows={3}
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        className="blog-editor__tags"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="blog-editor__tags-list">
          <AnimatePresence>
            {blog.tags.map((tag, index) => (
              <motion.span
                key={tag}
                className="blog-editor__tag"
                initial={{ opacity: 0, scale: 0.8, x: -20 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0.8, x: 20 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                whileHover={{ scale: 1.05, y: -2 }}
              >
                <FiTag className="icon" />
                {tag}
                <motion.button
                  onClick={() => handleRemoveTag(tag)}
                  className="blog-editor__tag-remove"
                  title={`Remove "${tag}" tag`}
                  whileHover={{ scale: 1.2, backgroundColor: "var(--color-error-500)" }}
                  whileTap={{ scale: 0.9 }}
                >
                  ×
                </motion.button>
              </motion.span>
            ))}
          </AnimatePresence>
        </div>
        <div className="blog-editor__tag-input">
          <motion.input
            type="text"
            placeholder="Add tag..."
            title="Add tags to categorize your blog post. Press Enter or click Add button to add a tag."
            value={newTag}
            onChange={e => setNewTag(e.target.value)}
            onKeyPress={e => e.key === "Enter" && handleAddTag()}
            whileFocus={{ scale: 1.02 }}
          />
          <motion.button
            onClick={handleAddTag}
            title="Add tag to your blog post (or press Enter)"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            disabled={!newTag.trim()}
          >
            <FiZap className="icon" />
            Add
          </motion.button>
        </div>
      </motion.div>

      <motion.div
        className="blog-editor__content"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <motion.div
          className="blog-editor__editor-panel"
          initial={{ opacity: 0, x: -30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <motion.div
            className="blog-editor__toolbar"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <div className="blog-editor__toolbar-group">
              <Tooltip content="Insert Heading - Creates a markdown heading (## Heading)">
                <motion.button
                  onClick={insertHeading}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiHash />
                </motion.button>
              </Tooltip>
              <Tooltip content="Bold Text (Ctrl+B) - Makes text bold (**bold text**)">
                <motion.button
                  onClick={insertBold}
                  whileHover={{ scale: 1.1, rotate: -5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiBold />
                </motion.button>
              </Tooltip>
              <Tooltip content="Italic Text (Ctrl+I) - Makes text italic (*italic text*)">
                <motion.button
                  onClick={insertItalic}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiItalic />
                </motion.button>
              </Tooltip>
            </div>

            <div className="blog-editor__toolbar-separator"></div>

            <div className="blog-editor__toolbar-group">
              <Tooltip content="Insert Bullet List - Creates an unordered list (- Item 1)">
                <motion.button
                  onClick={insertList}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiList />
                </motion.button>
              </Tooltip>
              <Tooltip content="Insert Blockquote - Creates a quote block (> Quote text)">
                <motion.button
                  onClick={insertQuote}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiMessageSquare />
                </motion.button>
              </Tooltip>
              <Tooltip content="Insert Horizontal Rule - Creates a divider line (---)">
                <motion.button
                  onClick={insertHorizontalRule}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiMinus />
                </motion.button>
              </Tooltip>
            </div>

            <div className="blog-editor__toolbar-separator"></div>

            <div className="blog-editor__toolbar-group">
              <Tooltip content="Insert Link (Ctrl+K) - Creates a clickable link [Link text](URL)">
                <motion.button
                  onClick={insertLink}
                  whileHover={{ scale: 1.1, rotate: -5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiLink />
                </motion.button>
              </Tooltip>
              <Tooltip content="Insert Image - Embeds an image ![Alt text](image-url)">
                <motion.button
                  onClick={insertImage}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiImage />
                </motion.button>
              </Tooltip>
              <Tooltip content="Insert Table - Creates a markdown table with headers and rows">
                <motion.button
                  onClick={insertTable}
                  whileHover={{ scale: 1.1, rotate: -5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiTable />
                </motion.button>
              </Tooltip>
              <Tooltip content="Insert Code Block - Creates syntax-highlighted code (```language)">
                <motion.button
                  onClick={insertCodeBlock}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FiCode />
                </motion.button>
              </Tooltip>
            </div>
          </motion.div>

          <motion.textarea
            ref={textareaRef}
            value={blog.content}
            onChange={handleContentChange}
            className="blog-editor__textarea"
            placeholder="Write your blog content in Markdown... Use the toolbar above for quick formatting or keyboard shortcuts: Ctrl+B (bold), Ctrl+I (italic), Ctrl+K (link), Ctrl+S (save)"
            title="Markdown Editor - Supports full markdown syntax with live preview. Use toolbar buttons or keyboard shortcuts for quick formatting."
            spellCheck="false"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            whileFocus={{
              boxShadow: "0 0 0 3px rgba(var(--color-primary-rgb), 0.1)",
              scale: 1.001,
            }}
          />
        </motion.div>

        <motion.div
          className="blog-editor__preview-panel"
          initial={{ opacity: 0, x: 30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <motion.div
            className="blog-editor__preview-header"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <h3>Live Preview</h3>
            <motion.button
              onClick={() => setIsDarkMode(!isDarkMode)}
              className="blog-editor__theme-toggle"
              title={`Switch to ${isDarkMode ? "light" : "dark"} theme for preview and editor`}
              whileHover={{ scale: 1.1, rotate: 180 }}
              whileTap={{ scale: 0.9 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              {isDarkMode ? "☀️" : "🌙"}
            </motion.button>
          </motion.div>
          <motion.div
            className="blog-editor__preview-content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            key={blog.content}
          >
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
              components={{
                code(props) {
                  const { children, className } = props;
                  const match = /language-(\w+)/.exec(className || "");
                  return match ? (
                    <SyntaxHighlighter
                      style={isDarkMode ? tomorrow : prism}
                      language={match[1]}
                      PreTag="div"
                    >
                      {String(children).replace(/\n$/, "")}
                    </SyntaxHighlighter>
                  ) : (
                    <code className={className}>{children}</code>
                  );
                },
              }}
            >
              {blog.content}
            </ReactMarkdown>
          </motion.div>
        </motion.div>
      </motion.div>

      <motion.div
        className="blog-editor__sidebar"
        initial={{ opacity: 0, x: 30 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="blog-editor__modification-history">
          <motion.h3
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            Modification History
          </motion.h3>
          <div className="blog-editor__timeline">
            <AnimatePresence>
              {blog.modificationHistory.length === 0 ? (
                <motion.p
                  className="blog-editor__no-history"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  No modifications yet. Save your work to start tracking changes.
                </motion.p>
              ) : (
                blog.modificationHistory.map((entry, index) => (
                  <motion.div
                    key={entry.id}
                    className="blog-editor__timeline-entry"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02, y: -2 }}
                  >
                    <motion.div
                      className="blog-editor__timeline-dot"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.7 + index * 0.1, type: "spring" }}
                    />
                    <div className="blog-editor__timeline-content">
                      <p className="blog-editor__timeline-description">{entry.description}</p>
                      <p className="blog-editor__timeline-meta">
                        {entry.author} • {entry.timestamp.toLocaleString()}
                      </p>
                    </div>
                  </motion.div>
                ))
              )}
            </AnimatePresence>
          </div>
        </div>
      </motion.div>
    </PageAnimation>
  );
};

export default BlogEditorPage;
