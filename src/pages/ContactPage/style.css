.contact-page {
  padding: 6rem 0;
  min-height: 100vh;
  background-color: var(--color-slate-50);
  background-image: 
    url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E%3Cg fill='%23a855f7' fill-opacity='0.05'%3E%3Cpath d='M0 0h40v40H0V0zm40 40h40v40H40V40zm0-40h2l-2 2V0zm0 4l4-4h2l-6 6V4zm0 4l8-8h2L40 10V8zm0 4L52 0h2L40 14v-2zm0 4L56 0h2L40 18v-2zm0 4L60 0h2L40 22v-2zm0 4L64 0h2L40 26v-2zm0 4L68 0h2L40 30v-2zm0 4L72 0h2L40 34v-2zm0 4L76 0h2L40 38v-2zm0 4L80 0v2L42 40h-2zm4 0L80 4v2L46 40h-2zm4 0L80 8v2L50 40h-2zm4 0l28-28v2L54 40h-2zm4 0l24-24v2L58 40h-2zm4 0l20-20v2L62 40h-2zm4 0l16-16v2L66 40h-2zm4 0l12-12v2L70 40h-2zm4 0l8-8v2l-6 6h-2zm4 0l4-4v2l-2 2h-2z'/%3E%3C/g%3E%3C/svg%3E"),
    linear-gradient(135deg, rgba(168, 85, 247, 0.08) 0%, rgba(99, 102, 241, 0.05) 100%);
  background-size: 80px 80px, cover;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.contact-header {
  text-align: center;
  margin-bottom: 4rem;
}

.contact-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 1rem;
}

.contact-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  position: relative;
  padding-top: 1rem;
}

.contact-subtitle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, var(--color-indigo-300), var(--color-purple-300));
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.contact-card {
  background: white;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid var(--color-slate-200);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.contact-card.social-card {
  grid-column: 1 / -1;
}

.card-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-indigo-500), var(--color-purple-500));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.contact-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: 0.5rem;
}

.contact-card p {
  color: var(--color-gray-600);
  margin-bottom: 1.5rem;
  font-size: var(--font-size-lg);
}

.contact-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-indigo-600);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all 0.2s ease;
  margin-top: auto;
}

.contact-link:hover {
  color: var(--color-purple-600);
  transform: translateX(5px);
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: var(--color-slate-100);
  color: var(--color-gray-700);
  font-size: 1.25rem;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: linear-gradient(135deg, var(--color-indigo-500), var(--color-purple-500));
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contact-page {
    padding: 4rem 0;
  }
  
  .contact-title {
    font-size: var(--font-size-3xl);
  }
  
  .contact-subtitle {
    font-size: var(--font-size-lg);
  }
  
  .contact-grid {
    grid-template-columns: 1fr;
  }
}
