.project-detail-page {
  min-height: 100vh;
  background-color: var(--color-slate-50);
}

.project-hero {
  position: relative;
  height: 50vh;
  min-height: 400px;
  background-size: cover;
  background-position: center;
  color: white;
  display: flex;
  align-items: flex-end;
  padding-bottom: 3rem;
}

.project-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.7));
  z-index: 1;
}

.project-hero .container {
  position: relative;
  z-index: 2;
}

.project-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.project-category {
  display: inline-block;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  padding: 0.5rem 1.25rem;
  border-radius: 2rem;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.project-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  margin: -3rem 0 4rem;
  position: relative;
  z-index: 10;
}

.project-info {
  background-color: var(--color-white);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: fit-content;
  border: 1px solid var(--color-slate-200);
}

.info-item {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--color-slate-200);
}

.info-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.info-item h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: 0.75rem;
}

.info-item p {
  color: var(--color-gray-700);
  margin: 0;
}

.technologies-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.technologies-list li {
  background-color: var(--color-slate-100);
  color: var(--color-gray-800);
  padding: 0.4rem 0.75rem;
  border-radius: 0.5rem;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
}

.technologies-list li:hover {
  background-color: var(--color-indigo-100);
  color: var(--color-indigo-700);
}

.project-link {
  display: inline-block;
  color: var(--color-indigo-600);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: color 0.2s ease;
}

.project-link:hover {
  color: var(--color-indigo-800);
  text-decoration: underline;
}

.project-description {
  background-color: var(--color-white);
  border-radius: 1rem;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-slate-200);
}

.project-description section {
  margin-bottom: 2.5rem;
}

.project-description section:last-child {
  margin-bottom: 0;
}

.project-description h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: 1.25rem;
  position: relative;
  padding-bottom: 0.75rem;
}

.project-description h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  border-radius: 3px;
}

.project-description p {
  color: var(--color-gray-700);
  line-height: 1.7;
  margin-bottom: 1rem;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.gallery-item {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.gallery-item img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.project-navigation {
  display: flex;
  justify-content: center;
  margin-bottom: 4rem;
}

.btn-secondary {
  display: inline-block;
  background-color: var(--color-white);
  color: var(--color-indigo-600);
  border: 2px solid var(--color-indigo-600);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  background-color: var(--color-indigo-600);
  color: white;
  box-shadow: 0 6px 12px rgba(99, 102, 241, 0.2);
}

.project-detail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: var(--color-gray-600);
}

.loader {
  border: 4px solid var(--color-slate-200);
  border-top: 4px solid var(--color-indigo-600);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.project-not-found {
  text-align: center;
  padding: 6rem 0;
  max-width: 600px;
  margin: 0 auto;
}

.project-not-found h2 {
  font-size: var(--font-size-3xl);
  color: var(--color-gray-900);
  margin-bottom: 1rem;
}

.project-not-found p {
  color: var(--color-gray-600);
  margin-bottom: 2rem;
}

.btn-primary {
  display: inline-block;
  background: linear-gradient(135deg, var(--color-indigo-600), var(--color-purple-600));
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
  box-shadow: 0 6px 15px rgba(99, 102, 241, 0.4);
  transform: translateY(-2px);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .project-content {
    grid-template-columns: 1fr;
  }
  
  .project-info {
    order: 2;
  }
  
  .project-description {
    order: 1;
    margin-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .project-hero {
    height: 40vh;
    min-height: 300px;
  }
  
  .project-title {
    font-size: var(--font-size-3xl);
  }
  
  .gallery-grid {
    grid-template-columns: 1fr;
  }
}
