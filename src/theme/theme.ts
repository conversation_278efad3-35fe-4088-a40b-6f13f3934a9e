// Main theme object
import { colors, lightTheme, darkTheme } from './colors';
import { typography } from './typography';
import { spacing, borderRadius } from './spacing';
import { shadows, darkShadows } from './shadows';
import { transitions } from './transitions';
import { breakpoints, mediaQueries } from './breakpoints';

export const theme = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  darkShadows,
  transitions,
  breakpoints,
  mediaQueries,
  
  // Theme variants
  light: {
    colors: lightTheme,
    shadows,
  },
  
  dark: {
    colors: darkTheme,
    shadows: darkShadows,
  },
} as const;

export type Theme = typeof theme;
export default theme;