// Color palette and theme colors
export const colors = {
  // Base colors
  white: "#ffffff",
  black: "#000000",

  // Gray scale
  gray: {
    50: "#f9fafb",
    100: "#f3f4f6",
    200: "#e5e7eb",
    300: "#d1d5db",
    400: "#9ca3af",
    500: "#6b7280",
    600: "#4b5563",
    700: "#374151",
    800: "#1f2937",
    900: "#111827",
  },

  // Primary colors (Indigo)
  primary: {
    50: "#eef2ff",
    100: "#e0e7ff",
    200: "#c7d2fe",
    300: "#a5b4fc",
    400: "#818cf8",
    500: "#6366f1",
    600: "#4f46e5",
    700: "#4338ca",
    800: "#3730a3",
    900: "#312e81",
  },

  // Secondary colors (Purple)
  secondary: {
    50: "#faf5ff",
    100: "#f3e8ff",
    200: "#e9d5ff",
    300: "#d8b4fe",
    400: "#c084fc",
    500: "#a855f7",
    600: "#9333ea",
    700: "#7e22ce",
    800: "#6b21a8",
    900: "#581c87",
  },

  // Semantic colors
  success: {
    50: "#f0fdf4",
    500: "#22c55e",
    600: "#16a34a",
  },
  warning: {
    50: "#fffbeb",
    500: "#f59e0b",
    600: "#d97706",
  },
  error: {
    50: "#fef2f2",
    500: "#ef4444",
    600: "#dc2626",
  },
  info: {
    50: "#eff6ff",
    500: "#3b82f6",
    600: "#2563eb",
  },
} as const;

// Theme-specific color mappings
export const lightTheme = {
  background: colors.white,
  backgroundRgb: "255, 255, 255",
  text: colors.gray[700],
  heading: colors.gray[900],
  primary: colors.primary[600],
  primaryRgb: "79, 70, 229",
  secondary: colors.secondary[600],
  secondaryRgb: "147, 51, 234",
  accent: colors.primary[400],
  muted: colors.gray[200],
  border: colors.gray[200],
  cardBg: colors.white,
  inputBg: colors.white,
  inputText: colors.gray[700],
  inputPlaceholder: colors.gray[400],
  inputBorder: colors.gray[300],
  inputFocusBorder: colors.primary[400],
  buttonText: colors.white,
  buttonHover: colors.primary[700],
  link: colors.primary[600],
  linkHover: colors.primary[700],
  success: colors.success[600],
  warning: colors.warning[600],
  error: colors.error[600],
  info: colors.info[600],
} as const;

export const darkTheme = {
  background: colors.gray[900],
  backgroundRgb: "17, 24, 39",
  text: colors.gray[300],
  heading: colors.white,
  primary: colors.primary[400],
  primaryRgb: "129, 140, 248",
  secondary: colors.secondary[400],
  secondaryRgb: "192, 132, 252",
  accent: colors.primary[300],
  muted: colors.gray[700],
  border: colors.gray[700],
  cardBg: colors.gray[800],
  inputBg: colors.gray[800],
  inputText: colors.gray[200],
  inputPlaceholder: colors.gray[500],
  inputBorder: colors.gray[600],
  inputFocusBorder: colors.primary[400],
  buttonText: colors.white,
  buttonHover: colors.primary[500],
  link: colors.primary[400],
  linkHover: colors.primary[300],
  success: colors.success[500],
  warning: colors.warning[500],
  error: colors.error[500],
  info: colors.info[500],
} as const;

export type ThemeColors = typeof lightTheme;
