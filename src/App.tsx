import React, { useState, Suspense, lazy, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import './App.css';
import { Header, Footer, BackToTop } from './components';
import { ParallaxProvider } from './contexts/ParallaxContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { ParallaxContextType } from './types/common';
import { cn } from './utils';
import { initializeLocalStorage } from './utils/localStorage';

// Lazy load pages for better performance
const HomePage = lazy(() => import('./pages/HomePage'));
const AboutPage = lazy(() => import('./pages/AboutPage'));
const ProjectsPage = lazy(() => import('./pages/ProjectsPage'));
const ProjectDetailPage = lazy(() => import('./pages/ProjectDetailPage'));
const ContactPage = lazy(() => import('./pages/ContactPage'));
const BlogEditorPage = lazy(() => import('./pages/BlogEditorPage'));
const BlogsPage = lazy(() => import('./pages/BlogsPage'));
const BlogViewPage = lazy(() => import('./pages/BlogViewPage'));

// Loading component
const PageLoader: React.FC = () => (
  <div className="page-loader">
    <div className="page-loader__spinner" />
    <p>Loading...</p>
  </div>
);

interface AppProps {
  className?: string;
}

const App: React.FC<AppProps> = ({ className = '' }) => {
  const [parallaxEnabled, setParallaxEnabled] = useState<boolean>(true);

  // Initialize localStorage cleanup on app start
  useEffect(() => {
    initializeLocalStorage();
  }, []);

  // Toggle parallax effect
  const toggleParallax = (): void => {
    setParallaxEnabled(prevState => !prevState);
  };

  // Create parallax context value
  const parallaxContextValue: ParallaxContextType = {
    enabled: parallaxEnabled,
    toggleParallax
  };

  const appClasses = cn('app-container', className);

  return (
    <ThemeProvider defaultTheme="light">
      <ParallaxProvider value={parallaxContextValue}>
        <div className={appClasses}>
          <Header />
          <main>
            <Suspense fallback={<PageLoader />}>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/about" element={<AboutPage />} />
                <Route path="/projects" element={<ProjectsPage />} />
                <Route path="/projects/:projectId" element={<ProjectDetailPage />} />
                <Route path="/contact" element={<ContactPage />} />
                <Route path="/blog-editor" element={<BlogEditorPage />} />
                <Route path="/blog-editor/:blogId" element={<BlogEditorPage />} />
                <Route path="/blogs" element={<BlogsPage />} />
                <Route path="/blog/:slug" element={<BlogViewPage />} />
              </Routes>
            </Suspense>
          </main>
          <Footer />
          <BackToTop />
        </div>
      </ParallaxProvider>
    </ThemeProvider>
  );
};

export default App;
