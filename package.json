{"name": "personal-website-react", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-slot": "1.2.3", "@radix-ui/react-tooltip": "1.2.7", "@tailwindcss/typography": "0.5.16", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "29.5.14", "@types/node": "22.13.10", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/react-router-dom": "5.3.3", "@types/react-syntax-highlighter": "15.5.13", "autoprefixer": "10.4.21", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "framer-motion": "12.5.0", "lucide-react": "0.539.0", "postcss": "8.5.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "4.12.0", "react-markdown": "10.1.0", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "react-syntax-highlighter": "15.6.1", "rehype-raw": "7.0.0", "remark-gfm": "4.0.1", "swiper": "11.2.10", "tailwind-merge": "3.3.1", "tailwindcss": "3.4.17", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "7.1.0"}}