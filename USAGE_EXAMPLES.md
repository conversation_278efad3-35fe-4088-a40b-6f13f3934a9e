# Usage Examples for Optimized Components

## 🎨 Theme System Usage

### Using the Enhanced Theme Provider
```tsx
import { ThemeProvider } from './contexts/ThemeContext';

function App() {
  return (
    <ThemeProvider defaultTheme="light">
      {/* Your app content */}
    </ThemeProvider>
  );
}
```

### Using Theme Hook
```tsx
import { useTheme } from './contexts/ThemeContext';

function MyComponent() {
  const { theme, toggleTheme, isDarkMode, themeConfig } = useTheme();
  
  return (
    <button onClick={toggleTheme}>
      Switch to {isDarkMode ? 'Light' : 'Dark'} Mode
    </button>
  );
}
```

## 🧩 UI Components Usage

### Button Component
```tsx
import { Button } from './components/ui';
import { FaDownload } from 'react-icons/fa';

function MyComponent() {
  return (
    <>
      {/* Primary button with icon */}
      <Button 
        variant="primary" 
        size="lg"
        leftIcon={<FaDownload />}
        onClick={() => console.log('Download')}
      >
        Download Resume
      </Button>
      
      {/* Loading button */}
      <Button 
        variant="secondary" 
        loading={isLoading}
        disabled={isLoading}
      >
        {isLoading ? 'Submitting...' : 'Submit'}
      </Button>
      
      {/* Full width button */}
      <Button variant="outline" fullWidth>
        Full Width Button
      </Button>
    </>
  );
}
```

### Original Card Component (with unique shapes)
```tsx
import { Card } from './components';

function ServiceCard() {
  return (
    <Card
      title="Web Development"
      content="Creating modern, responsive websites with cutting-edge technologies"
      icon="fas fa-code"
      color="indigo"
      onClick={() => console.log('Card clicked')}
    />
  );
}
```

### UI Card Component (flexible layout)
```tsx
import { UICard, CardHeader, CardBody, CardFooter, Button } from './components';

function ProjectCard() {
  return (
    <UICard variant="elevated" hover clickable>
      <CardHeader>
        <h3>Project Title</h3>
      </CardHeader>
      <CardBody>
        <p>Project description goes here...</p>
      </CardBody>
      <CardFooter>
        <Button variant="primary" size="sm">
          View Details
        </Button>
      </CardFooter>
    </UICard>
  );
}
```

### Container Component
```tsx
import { Container } from './components/ui';

function MyPage() {
  return (
    <Container size="lg" center padding>
      <h1>Page Content</h1>
      <p>This content is properly contained and responsive.</p>
    </Container>
  );
}
```

### Carousel Component (Swiper Replacement)
```tsx
import { Carousel } from './components/ui';

function ImageGallery() {
  const slides = [
    <img src="/image1.jpg" alt="Slide 1" />,
    <img src="/image2.jpg" alt="Slide 2" />,
    <img src="/image3.jpg" alt="Slide 3" />,
  ];

  return (
    <Carousel
      slides={slides}
      autoplay
      autoplayDelay={4000}
      navigation
      pagination
      loop
      breakpoints={{
        640: { slidesPerView: 1 },
        768: { slidesPerView: 2 },
        1024: { slidesPerView: 3 },
      }}
    />
  );
}
```

## 🎯 Enhanced ProjectCard Usage

```tsx
import { ProjectCard } from './components';
import { Project } from './types/common';

function ProjectsGrid() {
  const projects: Project[] = [
    {
      id: '1',
      title: 'Modern Website',
      category: 'Web Development',
      image: '/project1.jpg',
      description: 'A modern responsive website built with React',
      link: 'https://example.com'
    }
  ];

  return (
    <div className="projects-grid">
      {projects.map((project, index) => (
        <ProjectCard
          key={project.id}
          project={project}
          index={index}
          variant="default"
          showCategory
          onProjectClick={(project) => {
            // Custom click handler
            console.log('Clicked project:', project.title);
          }}
        />
      ))}
    </div>
  );
}
```

## 🛠️ Custom Hooks Usage

### useBreakpoint Hook
```tsx
import { useBreakpoint } from './hooks';

function ResponsiveComponent() {
  const { currentBreakpoint, isMobile, isTablet, isDesktop } = useBreakpoint();
  
  return (
    <div>
      <p>Current breakpoint: {currentBreakpoint}</p>
      {isMobile && <MobileLayout />}
      {isTablet && <TabletLayout />}
      {isDesktop && <DesktopLayout />}
    </div>
  );
}
```

### useLocalStorage Hook
```tsx
import { useLocalStorage } from './hooks';

function UserPreferences() {
  const [preferences, setPreferences, removePreferences] = useLocalStorage('userPrefs', {
    theme: 'light',
    language: 'en'
  });
  
  return (
    <div>
      <button onClick={() => setPreferences({ ...preferences, theme: 'dark' })}>
        Set Dark Theme
      </button>
      <button onClick={removePreferences}>
        Reset Preferences
      </button>
    </div>
  );
}
```

### useScrollAnimation Hook
```tsx
import { useScrollAnimation } from './hooks';

function AnimatedSection() {
  const { elementRef, isVisible, style } = useScrollAnimation({
    animation: 'fade-up',
    threshold: 0.2,
    delay: 200,
    once: true
  });
  
  return (
    <div ref={elementRef} style={style}>
      <h2>This section animates on scroll</h2>
      <p>Content appears with a fade-up animation</p>
    </div>
  );
}
```

## 🎨 Utility Functions Usage

### Class Names Utility
```tsx
import { cn } from './utils';

function ConditionalComponent({ isActive, size, className }) {
  return (
    <div className={cn(
      'base-class',
      isActive && 'active-class',
      `size-${size}`,
      className
    )}>
      Content
    </div>
  );
}
```

### Formatters
```tsx
import { formatDate, truncateText, slugify } from './utils';

function BlogPost({ post }) {
  return (
    <article>
      <h1>{post.title}</h1>
      <time>{formatDate(post.publishedAt)}</time>
      <p>{truncateText(post.excerpt, 150)}</p>
      <a href={`/blog/${slugify(post.title)}`}>Read More</a>
    </article>
  );
}
```

### Validators
```tsx
import { isValidEmail, isRequired } from './utils';

function ContactForm() {
  const [email, setEmail] = useState('');
  const [errors, setErrors] = useState({});
  
  const validateForm = () => {
    const newErrors = {};
    
    if (!isRequired(email)) {
      newErrors.email = 'Email is required';
    } else if (!isValidEmail(email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      if (validateForm()) {
        // Submit form
      }
    }}>
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        className={errors.email ? 'error' : ''}
      />
      {errors.email && <span className="error-message">{errors.email}</span>}
      <Button type="submit">Submit</Button>
    </form>
  );
}
```

## 🎨 CSS Custom Properties Usage

The theme system automatically injects CSS custom properties that you can use in your styles:

```css
.my-component {
  background-color: var(--color-background);
  color: var(--color-text);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal) var(--ease-custom);
}

.my-component:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}
```

## 📱 Responsive Design

Use the breakpoint utilities for consistent responsive design:

```css
.responsive-grid {
  display: grid;
  gap: var(--spacing-4);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-8);
  }
}
```

This optimized system provides a solid foundation for building modern, maintainable React applications with consistent design and excellent developer experience!